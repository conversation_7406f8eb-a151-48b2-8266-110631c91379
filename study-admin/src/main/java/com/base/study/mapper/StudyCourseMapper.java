package com.base.study.mapper;

import java.util.Date;
import java.util.List;

import com.base.study.domain.StudyCourse;
import org.apache.ibatis.annotations.Param;

/**
 * 课程Mapper接口
 *
 * <AUTHOR>
 * @date 2023-04-13
 */
public interface StudyCourseMapper
{
    /**
     * 查询课程
     *
     * @param courseId 课程主键
     * @return 课程
     */
    public StudyCourse selectStudyCourseByCourseId(Long courseId);

    /**
     * 查询课程列表
     *
     * @param studyCourse 课程
     * @return 课程集合
     */
    public List<StudyCourse> selectStudyCourseList(StudyCourse studyCourse);

    /**
     * 新增课程
     *
     * @param studyCourse 课程
     * @return 结果
     */
    public int insertStudyCourse(StudyCourse studyCourse);

    /**
     * 修改课程
     *
     * @param studyCourse 课程
     * @return 结果
     */
    public int updateStudyCourse(StudyCourse studyCourse);

    /**
     * 删除课程
     *
     * @param courseId 课程主键
     * @return 结果
     */
    public int deleteStudyCourseByCourseId(Long courseId);

    /**
     * 批量删除课程
     *
     * @param courseIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteStudyCourseByCourseIds(Long[] courseIds);

    /**
     * 根据用户ID和时间，查询用户当天需要上的课程
     * （打卡时间 在 课堂开始时间前30分钟 到 结束时间后30分钟 内）
     *
     * @param userId 用户ID
     * @param punchTime 打卡时间
     * @return 课堂列表
     */
    public List<StudyCourse> selectTodayCoursesByUserId(@Param("userId") Long userId, @Param("punchTime") Date punchTime);


    public List<StudyCourse> selectTodayCoursesByUserIdAndStartTime(@Param("userId") Long userId,@Param("nowData") Date nowData);
}
