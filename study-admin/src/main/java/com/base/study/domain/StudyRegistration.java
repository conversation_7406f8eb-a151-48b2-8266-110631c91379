package com.base.study.domain;

import java.math.BigDecimal;
import java.util.Date;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.base.common.annotation.Excel;
import com.base.common.core.domain.BaseEntity;
import com.base.common.core.domain.entity.SysUser;

/**
 * 学员报名表对象 study_registration
 * 
 * <AUTHOR>
 * @date 2025-06-19
 */
public class StudyRegistration extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long registrationId;

    /** 报名编号 */
    @Excel(name = "报名编号")
    private String registrationNo;

    /** 关联班次ID */
    @Excel(name = "关联班次ID")
    private Long classId;

    /** 关联学员ID (sys_user) */
    @Excel(name = "关联学员ID")
    private Long userId;

    /** 报名时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Excel(name = "报名时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date registrationTime;
    
    /** 应修总学时 */
//    @Excel(name = "应修总学时")
    private BigDecimal totalHours;

    /** 应修总学时 (别名) */
    @Excel(name = "应修总学时")
    private BigDecimal requiredHours;

    /** 已完成学时 */
//    @Excel(name = "已完成学时")
    private BigDecimal completedHours;

    /** 已获得学时 */
    @Excel(name = "已获得学时")
    private BigDecimal achievedHours;

    /** 完成率 */
    @Excel(name = "完成率")
    private BigDecimal completionRate;

    /** 最终得分 */
    @Excel(name = "最终得分")
    private BigDecimal score;

    /** 已获学分 */
//    @Excel(name = "已获学分")
    private BigDecimal earnedCredits;

    /** 状态 (0-待审核, 1-已通过, 2-已拒绝, 3-学习中, 4-已结业, 5-已退班) */
    @Excel(name = "状态", readConverterExp = "0=待审核,1=已通过,2=已拒绝,3=学习中,4=已结业,5=已退班")
    private String status;
    
    /** 删除标志（0代表存在 2代表删除） */
    private String delFlag;

    /** 电话号码 */
    private String phonenumber;

    // 关联班次
    private StudyClass studyClass;
    
    // 关联用户
    private SysUser sysUser;

    public StudyClass getStudyClass() {
        return studyClass;
    }

    public void setStudyClass(StudyClass studyClass) {
        this.studyClass = studyClass;
    }

    public SysUser getSysUser() {
        return sysUser;
    }

    public void setSysUser(SysUser sysUser) {
        this.sysUser = sysUser;
    }

    public void setRegistrationId(Long registrationId)
    {
        this.registrationId = registrationId;
    }

    public Long getRegistrationId()
    {
        return registrationId;
    }

    public void setRegistrationNo(String registrationNo)
    {
        this.registrationNo = registrationNo;
    }

    public String getRegistrationNo()
    {
        return registrationNo;
    }
    public void setClassId(Long classId) 
    {
        this.classId = classId;
    }

    public Long getClassId() 
    {
        return classId;
    }
    public void setUserId(Long userId) 
    {
        this.userId = userId;
    }

    public Long getUserId() 
    {
        return userId;
    }
    public void setRegistrationTime(Date registrationTime) 
    {
        this.registrationTime = registrationTime;
    }

    public Date getRegistrationTime() 
    {
        return registrationTime;
    }
    public void setTotalHours(BigDecimal totalHours) 
    {
        this.totalHours = totalHours;
    }

    public BigDecimal getTotalHours()
    {
        return totalHours;
    }

    public void setRequiredHours(BigDecimal requiredHours)
    {
        this.requiredHours = requiredHours;
        // Also set totalHours for compatibility
        this.totalHours = requiredHours;
    }

    public BigDecimal getRequiredHours()
    {
        return requiredHours != null ? requiredHours : totalHours;
    }
    public void setCompletedHours(BigDecimal completedHours) 
    {
        this.completedHours = completedHours;
    }

    public BigDecimal getCompletedHours() 
    {
        return completedHours;
    }
    public void setAchievedHours(BigDecimal achievedHours) 
    {
        this.achievedHours = achievedHours;
    }

    public BigDecimal getAchievedHours() 
    {
        return achievedHours;
    }
    public void setCompletionRate(BigDecimal completionRate) 
    {
        this.completionRate = completionRate;
    }

    public BigDecimal getCompletionRate() 
    {
        return completionRate;
    }
    public void setScore(BigDecimal score) 
    {
        this.score = score;
    }

    public BigDecimal getScore() 
    {
        return score;
    }
    public void setEarnedCredits(BigDecimal earnedCredits) 
    {
        this.earnedCredits = earnedCredits;
    }

    public BigDecimal getEarnedCredits() 
    {
        return earnedCredits;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }
    public void setDelFlag(String delFlag) 
    {
        this.delFlag = delFlag;
    }

    public String getDelFlag() 
    {
        return delFlag;
    }

    public String getPhonenumber() {
        return phonenumber;
    }

    public void setPhonenumber(String phonenumber) {
        this.phonenumber = phonenumber;
    }

    @Override
    public String toString() {
        return "StudyRegistration{" +
                "registrationId=" + registrationId +
                ", registrationNo='" + registrationNo + '\'' +
                ", classId=" + classId +
                ", userId=" + userId +
                ", registrationTime=" + registrationTime +
                ", totalHours=" + totalHours +
                ", requiredHours=" + requiredHours +
                ", completedHours=" + completedHours +
                ", achievedHours=" + achievedHours +
                ", completionRate=" + completionRate +
                ", score=" + score +
                ", earnedCredits=" + earnedCredits +
                ", status='" + status + '\'' +
                ", delFlag='" + delFlag + '\'' +
                ", phonenumber='" + phonenumber + '\'' +
                ", studyClass=" + studyClass +
                ", sysUser=" + sysUser +
                '}';
    }
}