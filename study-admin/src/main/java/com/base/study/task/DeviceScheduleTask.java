package com.base.study.task;

import com.base.common.utils.DateUtils;
import com.base.study.domain.StudyAttendanceDevice;
import com.base.study.domain.StudyCourse;
import com.base.study.mapper.StudyAttendanceDeviceMapper;
import com.base.study.mapper.StudyCourseMapper;
import com.base.study.service.DeviceService;
import com.base.study.service.StudyGuardService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * 考勤设备定时任务
 * 1. 课程开始前2小时自动同步学员到设备
 * 2. 课程结束后2小时自动删除学员
 * 3. 布防开启后自动同步考勤记录
 * 
 * <AUTHOR>
 * @date 2025-01-03
 */
@Component
public class DeviceScheduleTask {
    
    private static final Logger log = LoggerFactory.getLogger(DeviceScheduleTask.class);
    
    @Autowired
    private DeviceService deviceService;
    
    @Autowired
    private StudyCourseMapper courseMapper;
    
    @Autowired
    private StudyAttendanceDeviceMapper deviceMapper;
    
    /**
     * 每30分钟检查一次课程，同步学员到设备
     * 在课程开始前2小时执行
     */
    @Scheduled(fixedRate = 30 * 60 * 1000) // 30分钟执行一次
    public void syncStudentsBeforeCourse() {
        try {
            log.debug("开始检查需要同步学员的课程...");
            
            // 获取未来2-3小时内开始的课程
            Date now = new Date();
            Date startTime = DateUtils.addHours(now, 2);
            Date endTime = DateUtils.addHours(now, 3);
            
            // 查询时间范围内的课程
            StudyCourse queryParam = new StudyCourse();
            List<StudyCourse> courses = courseMapper.selectStudyCourseList(queryParam);
            
            int syncCount = 0;
            for (StudyCourse course : courses) {
                if (course.getStartTime() != null && 
                    course.getStartTime().after(startTime) && 
                    course.getStartTime().before(endTime)) {
                    
                    // 同步班级学员到设备
                    int result = deviceService.syncClassStudentsToDevice(course.getClassId());
                    if (result > 0) {
                        syncCount++;
                        log.info("课程[{}]学员同步完成，班级ID: {}, 同步人数: {}", 
                            course.getCourseName(), course.getClassId(), result);
                    }
                }
            }
            
            if (syncCount > 0) {
                log.info("本次学员同步任务完成，共处理{}门课程", syncCount);
            }
            
        } catch (Exception e) {
            log.error("同步学员到设备任务执行失败: {}", e.getMessage());
        }
    }
    
    /**
     * 每30分钟检查一次课程，从设备删除学员
     * 在课程结束后2小时执行
     */
    @Scheduled(fixedRate = 30 * 60 * 1000) // 30分钟执行一次
    public void removeStudentsAfterCourse() {
        try {
            log.debug("开始检查需要删除学员的课程...");
            
            // 获取2-3小时前结束的课程
            Date now = new Date();
            Date startTime = DateUtils.addHours(now, -3);
            Date endTime = DateUtils.addHours(now, -2);
            
            // 查询时间范围内的课程
            StudyCourse queryParam = new StudyCourse();
            List<StudyCourse> courses = courseMapper.selectStudyCourseList(queryParam);
            
            int removeCount = 0;
            for (StudyCourse course : courses) {
                if (course.getEndTime() != null && 
                    course.getEndTime().after(startTime) && 
                    course.getEndTime().before(endTime)) {
                    
                    // 从设备删除班级学员
                    int result = deviceService.removeClassStudentsFromDevice(course.getClassId());
                    if (result > 0) {
                        removeCount++;
                        log.info("课程[{}]学员删除完成，班级ID: {}, 删除人数: {}", 
                            course.getCourseName(), course.getClassId(), result);
                    }
                }
            }
            
            if (removeCount > 0) {
                log.info("本次学员删除任务完成，共处理{}门课程", removeCount);
            }
            
        } catch (Exception e) {
            log.error("从设备删除学员任务执行失败: {}", e.getMessage());
        }
    }
    
    /**
     * 每10分钟同步一次考勤记录
     * 只同步布防开启的设备
     */
    @Scheduled(fixedRate = 10 * 60 * 1000) // 10分钟执行一次
    public void syncAttendanceRecords() {
        try {
            log.debug("开始同步考勤记录...");
            
            // 获取所有启用布防的设备
            StudyAttendanceDevice queryParam = new StudyAttendanceDevice();
            queryParam.setStatus("1"); // 启用状态
            List<StudyAttendanceDevice> devices = deviceMapper.selectStudyAttendanceDeviceList(queryParam);
            
            // 过滤出布防开启的设备（这里假设有个字段标识布防状态）
            List<StudyAttendanceDevice> guardEnabledDevices = devices.stream()
                .filter(device -> isGuardEnabled(device))
                .collect(java.util.stream.Collectors.toList());
            
            if (guardEnabledDevices.isEmpty()) {
                log.debug("没有启用布防的设备，跳过考勤记录同步");
                return;
            }
            
            // 同步最近10分钟的考勤记录
            Date endTime = new Date();
            Date startTime = DateUtils.addMinutes(endTime, -10);
            
            int totalSyncCount = 0;
            for (StudyAttendanceDevice device : guardEnabledDevices) {
                try {
                    int syncCount = deviceService.syncDeviceAttendanceRecords(device, startTime, endTime);
                    totalSyncCount += syncCount;
                    
                    if (syncCount > 0) {
                        log.info("设备[{}]考勤记录同步完成: {}条", device.getDeviceName(), syncCount);
                    }
                } catch (Exception e) {
                    log.error("设备[{}]考勤记录同步失败: {}", device.getDeviceName(), e.getMessage());
                }
            }
            
            if (totalSyncCount > 0) {
                log.info("本次考勤记录同步完成，共同步{}条记录", totalSyncCount);
            }
            
        } catch (Exception e) {
            log.error("考勤记录同步任务执行失败: {}", e.getMessage());
        }
    }
    
    /**
     * 每小时启动一次布防检查
     * 自动启动应该布防的设备
     */
    @Scheduled(fixedRate = 60 * 60 * 1000) // 1小时执行一次
    public void autoStartGuard() {
        try {
            log.debug("开始检查需要启动布防的设备...");
            
            // 获取当前时间，查找正在进行的课程
            Date now = new Date();
            Date startTime = DateUtils.addHours(now, -1); // 1小时前开始
            Date endTime = DateUtils.addHours(now, 1);    // 1小时后结束
            
            // 查询正在进行的课程
            StudyCourse queryParam = new StudyCourse();
            List<StudyCourse> courses = courseMapper.selectStudyCourseList(queryParam);
            
            // 找到需要布防的课程
            for (StudyCourse course : courses) {
                if (course.getStartTime() != null && course.getEndTime() != null &&
                    now.after(course.getStartTime()) && now.before(course.getEndTime())) {
                    
                    // 启动该班级关联设备的布防
                    startGuardForClass(course.getClassId());
                }
            }
            
        } catch (Exception e) {
            log.error("自动启动布防任务执行失败: {}", e.getMessage());
        }
    }
    
    /**
     * 启动班级关联设备的布防
     */
    private void startGuardForClass(Long classId) {
        try {
            // 这里需要通过班级ID获取设备列表，然后启动布防
            // 由于需要查询班级信息，这里简化处理
            log.info("准备启动班级[{}]关联设备的布防", classId);
            
            // 获取所有设备，检查是否需要启动布防
            StudyAttendanceDevice queryParam = new StudyAttendanceDevice();
            queryParam.setStatus("1");
            List<StudyAttendanceDevice> devices = deviceMapper.selectStudyAttendanceDeviceList(queryParam);
            
            for (StudyAttendanceDevice device : devices) {
                if (!isGuardEnabled(device)) {
                    boolean result = deviceService.startGuard(device);
                    if (result) {
                        log.info("设备[{}]布防启动成功", device.getDeviceName());
                        // 这里应该更新设备的布防状态，但由于字段不确定，暂时省略
                    }
                }
            }
            
        } catch (Exception e) {
            log.error("启动班级[{}]设备布防失败: {}", classId, e.getMessage());
        }
    }
    
    /**
     * 判断设备是否已启用布防
     */
    private boolean isGuardEnabled(StudyAttendanceDevice device) {
        return "1".equals(device.getGuardEnabled());
    }
} 