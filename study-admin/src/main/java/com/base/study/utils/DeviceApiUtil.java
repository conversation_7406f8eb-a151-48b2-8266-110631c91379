package com.base.study.utils;

import com.base.common.utils.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.client.methods.HttpDelete;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpPut;
import org.apache.http.entity.StringEntity;
import org.apache.http.entity.mime.MultipartEntityBuilder;
import org.apache.http.entity.ContentType;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.nio.charset.StandardCharsets;

/**
 * 考勤设备API工具类
 * 用于与考勤设备进行HTTP通信
 * 
 * <AUTHOR>
 * @date 2025-01-03
 */
public class DeviceApiUtil {
    
    private static final Logger log = LoggerFactory.getLogger(DeviceApiUtil.class);
    
    /**
     * 发送GET请求
     */
    public static String get(String url, String username, String password) throws Exception {
        log.info("[考勤机API][GET] 请求地址: {}, 用户名: {}", url, username);
        try (CloseableHttpClient httpClient = createHttpClient(username, password)) {
            HttpGet httpGet = new HttpGet(url);
            httpGet.setHeader("Content-Type", "application/json");
            
            HttpResponse response = httpClient.execute(httpGet);
            String result = handleResponse(response);
            log.info("[考勤机API][GET] 响应: {}", result);
            return result;
        } catch (Exception e) {
            log.error("[考勤机API][GET] 异常: {}", e.getMessage(), e);
            throw e;
        }
    }
    
    /**
     * 发送POST请求
     */
    public static String post(String url, String requestBody, String username, String password) throws Exception {
        log.info("[考勤机API][POST] 请求地址: {}, 用户名: {}, 入参: {}", url, username, requestBody);
        try (CloseableHttpClient httpClient = createHttpClient(username, password)) {
            HttpPost httpPost = new HttpPost(url);
            httpPost.setHeader("Content-Type", "application/json");
            
            if (StringUtils.isNotEmpty(requestBody)) {
                StringEntity entity = new StringEntity(requestBody, StandardCharsets.UTF_8);
                httpPost.setEntity(entity);
            }
            
            HttpResponse response = httpClient.execute(httpPost);
            String result = handleResponse(response);
            log.info("[考勤机API][POST] 响应: {}", result);
            return result;
        } catch (Exception e) {
            log.error("[考勤机API][POST] 异常: {}", e.getMessage(), e);
            throw e;
        }
    }
    
    /**
     * 发送PUT请求
     */
    public static String put(String url, String requestBody, String username, String password) throws Exception {
        log.info("[考勤机API][PUT] 请求地址: {}, 用户名: {}, 入参: {}", url, username, requestBody);
        try (CloseableHttpClient httpClient = createHttpClient(username, password)) {
            HttpPut httpPut = new HttpPut(url);
            
            // 根据内容判断Content-Type
            if (StringUtils.isNotEmpty(requestBody)) {
                if (requestBody.trim().startsWith("<?xml") || requestBody.trim().startsWith("<")) {
                    httpPut.setHeader("Content-Type", "application/xml");
                } else {
                    httpPut.setHeader("Content-Type", "application/json");
                }
                StringEntity entity = new StringEntity(requestBody, StandardCharsets.UTF_8);
                httpPut.setEntity(entity);
            } else {
                httpPut.setHeader("Content-Type", "application/json");
            }
            
            HttpResponse response = httpClient.execute(httpPut);
            String result = handleResponse(response);
            log.info("[考勤机API][PUT] 响应: {}", result);
            return result;
        } catch (Exception e) {
            log.error("[考勤机API][PUT] 异常: {}", e.getMessage(), e);
            throw e;
        }
    }
    
    /**
     * 发送PUT请求（XML格式）
     */
    public static String putXml(String url, String xmlBody, String username, String password) throws Exception {
        log.info("[考勤机API][PUT-XML] 请求地址: {}, 用户名: {}, 入参: {}", url, username, xmlBody);
        try (CloseableHttpClient httpClient = createHttpClient(username, password)) {
            HttpPut httpPut = new HttpPut(url);
            httpPut.setHeader("Content-Type", "application/xml");
            
            if (StringUtils.isNotEmpty(xmlBody)) {
                StringEntity entity = new StringEntity(xmlBody, StandardCharsets.UTF_8);
                httpPut.setEntity(entity);
            }
            
            HttpResponse response = httpClient.execute(httpPut);
            String result = handleResponse(response);
            log.info("[考勤机API][PUT-XML] 响应: {}", result);
            return result;
        } catch (Exception e) {
            log.error("[考勤机API][PUT-XML] 异常: {}", e.getMessage(), e);
            throw e;
        }
    }
    
    /**
     * 发送DELETE请求
     */
    public static String delete(String url, String username, String password) throws Exception {
        log.info("[考勤机API][DELETE] 请求地址: {}, 用户名: {}", url, username);
        try (CloseableHttpClient httpClient = createHttpClient(username, password)) {
            HttpDelete httpDelete = new HttpDelete(url);
            httpDelete.setHeader("Content-Type", "application/json");

            HttpResponse response = httpClient.execute(httpDelete);
            String result = handleResponse(response);
            log.info("[考勤机API][DELETE] 响应: {}", result);
            return result;
        } catch (Exception e) {
            log.error("[考勤机API][DELETE] 异常: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 发送multipart/form-data POST请求（用于文件上传）
     */
    public static String postMultipart(String url, String faceDataRecord, byte[] imageBytes,
                                     String username, String password) throws Exception {
        log.info("[考勤机API][POST-MULTIPART] 请求地址: {}, 用户名: {}, 图片大小: {}字节",
            url, username, imageBytes.length);

        try (CloseableHttpClient httpClient = createHttpClient(username, password)) {
            HttpPost httpPost = new HttpPost(url);

            // 构建multipart实体
            MultipartEntityBuilder builder = MultipartEntityBuilder.create();

            // 添加FaceDataRecord文本字段
            builder.addTextBody("FaceDataRecord", faceDataRecord,
                ContentType.TEXT_PLAIN.withCharset(StandardCharsets.UTF_8));

            // 添加FaceImage文件字段
            builder.addBinaryBody("FaceImage", imageBytes,
                ContentType.create("image/jpeg"), "face.jpg");

            HttpEntity multipartEntity = builder.build();
            httpPost.setEntity(multipartEntity);

            HttpResponse response = httpClient.execute(httpPost);
            String result = handleResponse(response);
            log.info("[考勤机API][POST-MULTIPART] 响应: {}", result);
            return result;

        } catch (Exception e) {
            log.error("[考勤机API][POST-MULTIPART] 异常: {}", e.getMessage(), e);
            throw e;
        }
    }
    
    /**
     * 创建带认证的HTTP客户端
     */
    private static CloseableHttpClient createHttpClient(String username, String password) {
        CredentialsProvider credentialsProvider = new BasicCredentialsProvider();
        credentialsProvider.setCredentials(
            AuthScope.ANY,
            new UsernamePasswordCredentials(username, password)
        );
        
        return HttpClients.custom()
            .setDefaultCredentialsProvider(credentialsProvider)
            .build();
    }
    
    /**
     * 处理HTTP响应
     */
    private static String handleResponse(HttpResponse response) throws IOException {
        int statusCode = response.getStatusLine().getStatusCode();
        HttpEntity entity = response.getEntity();
        
        String responseBody = "";
        if (entity != null) {
            responseBody = EntityUtils.toString(entity, StandardCharsets.UTF_8);
        }
        
        if (statusCode >= 200 && statusCode < 300) {
            log.debug("HTTP请求成功: 状态码[{}], 响应[{}]", statusCode, responseBody);
            return responseBody;
        } else {
            log.error("HTTP请求失败: 状态码[{}], 响应[{}]", statusCode, responseBody);
            throw new RuntimeException("HTTP请求失败: " + statusCode + " - " + responseBody);
        }
    }
} 