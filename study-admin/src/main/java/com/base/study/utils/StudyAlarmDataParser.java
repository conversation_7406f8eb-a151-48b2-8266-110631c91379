package com.base.study.utils;

import com.alibaba.fastjson2.JSONObject;
import com.base.study.service.RealTimeEventService;
import com.base.common.utils.spring.SpringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

/**
 * 学习系统报警事件解析处理逻辑
 * 扩展原有的AlarmDataParser，增加实时考勤事件处理
 * 
 * <AUTHOR>
 * @date 2025-07-01
 */
@Slf4j
public class StudyAlarmDataParser {

    /**
     * 解析数据并处理实时事件
     *
     * @param contentType   要解析的类型
     * @param storeFolderPath 保存文件路径
     * @param alarmInfo 事件信息
     * @param byteArray 二进制数据
     * @param deviceIp 设备IP地址
     */
    public void parseAlarmInfo(int contentType, String storeFolderPath, String alarmInfo, byte[] byteArray, String deviceIp) {
        // 处理文本类型的事件数据
        if (alarmInfo != null) {
            switch (contentType) {
                case 3: // APPLICATION_JSON
                case 4: // APPLICATION_XML
                    handleAlarmBodyInfo(storeFolderPath, contentType, alarmInfo, deviceIp);
                    break;
                default:
                    log.debug("未处理的内容类型: {}", contentType);
                    break;
            }
        }

        // 处理二进制数据（如图片等）
        if (byteArray != null) {
            handleByteAryFile(storeFolderPath, contentType, byteArray);
        }
    }

    /**
     * 处理报警事件数据内容
     *
     * @param storeFolderPath 存储路径
     * @param contentType 内容类型
     * @param alarmInfo 事件信息
     * @param deviceIp 设备IP地址
     */
    private void handleAlarmBodyInfo(String storeFolderPath, int contentType, String alarmInfo, String deviceIp) {
        String eventType = "";
        String processedEventData = "";
        
        try {
            switch (contentType) {
                case 3: { // APPLICATION_JSON
                    JSONObject jsonAlarmRecv = JSONObject.parseObject(alarmInfo);
                    if (jsonAlarmRecv.containsKey("eventType")) {
                        eventType = jsonAlarmRecv.getString("eventType");
                    }
                    processedEventData = alarmInfo;
                    break;
                }
                case 4: { // APPLICATION_XML
                    // XML转JSON处理
                    try {
                        // 简单的XML解析，提取eventType
                        if (alarmInfo.contains("<eventType>")) {
                            int start = alarmInfo.indexOf("<eventType>") + 11;
                            int end = alarmInfo.indexOf("</eventType>");
                            if (end > start) {
                                eventType = alarmInfo.substring(start, end);
                            }
                        }
                        
                        // 将XML转换为JSON格式用于后续处理
                        processedEventData = convertXmlToJson(alarmInfo);
                    } catch (Exception e) {
                        log.warn("XML解析失败，使用原始数据: {}", e.getMessage());
                        processedEventData = alarmInfo;
                    }
                    break;
                }
                default: {
                    log.warn("未匹配到可以解析的content-type: {}", contentType);
                    return;
                }
            }

            // 记录事件信息
            log.debug("收到设备事件: deviceIp={}, eventType={}", deviceIp, eventType);

            // 实时处理考勤事件
            if (!StringUtils.isEmpty(deviceIp) && !StringUtils.isEmpty(processedEventData)) {
                try {
                    RealTimeEventService realTimeEventService = SpringUtils.getBean(RealTimeEventService.class);
                    if (realTimeEventService != null) {
                        boolean processed = realTimeEventService.processRealTimeAttendanceEvent(deviceIp, processedEventData);
                        if (processed) {
                            log.info("实时考勤事件处理成功: deviceIp={}, eventType={}", deviceIp, eventType);
                        }
                    } else {
                        log.warn("无法获取RealTimeEventService实例");
                    }
                } catch (Exception e) {
                    log.error("实时事件处理失败: deviceIp={}, error={}", deviceIp, e.getMessage());
                }
            }

            // 保存事件数据到文件（保持原有功能）
            saveEventToFile(storeFolderPath, eventType, contentType, alarmInfo);

        } catch (Exception e) {
            log.error("事件处理异常: deviceIp={}, error={}", deviceIp, e.getMessage(), e);
        }
    }

    /**
     * 简单的XML转JSON转换
     */
    private String convertXmlToJson(String xmlData) {
        try {
            JSONObject jsonObject = new JSONObject();
            
            // 提取常见的考勤事件字段
            String[] fields = {"eventType", "employeeNoString", "time", "name", "cardNo"};
            
            for (String field : fields) {
                String startTag = "<" + field + ">";
                String endTag = "</" + field + ">";
                
                if (xmlData.contains(startTag) && xmlData.contains(endTag)) {
                    int start = xmlData.indexOf(startTag) + startTag.length();
                    int end = xmlData.indexOf(endTag);
                    if (end > start) {
                        String value = xmlData.substring(start, end).trim();
                        jsonObject.put(field, value);
                    }
                }
            }
            
            return jsonObject.toJSONString();
        } catch (Exception e) {
            log.warn("XML转JSON失败: {}", e.getMessage());
            return "{}";
        }
    }

    /**
     * 保存事件数据到文件
     */
    private void saveEventToFile(String storeFolderPath, String eventType, int contentType, String alarmInfo) {
        try {
            String curTimeStampStr = getCurrentTimestamp();
            String filename = curTimeStampStr + "_eventType_" + eventType;
            String fileExtension = getFileExtension(contentType);
            
            // 这里可以调用文件保存逻辑
            log.debug("保存事件文件: path={}, filename={}, extension={}", storeFolderPath, filename, fileExtension);
            
            // TODO: 实现文件保存逻辑
            // FileUtil.output2File(storeFolderPath, filename, fileExtension, alarmInfo);
            
        } catch (Exception e) {
            log.error("保存事件文件失败: {}", e.getMessage());
        }
    }

    /**
     * 处理二进制数据文件
     */
    private void handleByteAryFile(String storeFolderPath, int contentType, byte[] byteArray) {
        try {
            String curTimeStampStr = getCurrentTimestamp();
            String fileExtension = getFileExtension(contentType);
            
            log.debug("保存二进制文件: path={}, size={}, extension={}", storeFolderPath, byteArray.length, fileExtension);
            
            // TODO: 实现二进制文件保存逻辑
            // FileUtil.byteAry2File(storeFolderPath, curTimeStampStr, fileExtension, byteArray);
            
        } catch (Exception e) {
            log.error("保存二进制文件失败: {}", e.getMessage());
        }
    }

    /**
     * 获取当前时间戳
     */
    private String getCurrentTimestamp() {
        return String.valueOf(System.currentTimeMillis());
    }

    /**
     * 根据内容类型获取文件扩展名
     */
    private String getFileExtension(int contentType) {
        switch (contentType) {
            case 3: return ".json";
            case 4: return ".xml";
            case 7: return ".jpeg";
            case 8: return ".png";
            case 11: return ".mp3";
            case 12: return ".ps";
            case 14: return ".zip";
            default: return ".data";
        }
    }
}
