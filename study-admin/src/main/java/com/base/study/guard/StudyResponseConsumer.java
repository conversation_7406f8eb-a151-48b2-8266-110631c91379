package com.base.study.guard;

import com.base.study.utils.StudyAlarmDataParser;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpResponse;
import org.apache.http.nio.IOControl;
import org.apache.http.nio.client.methods.AsyncCharConsumer;
import org.apache.http.protocol.HttpContext;

import java.io.IOException;
import java.nio.CharBuffer;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;

/**
 * 学习系统响应消费者
 * 扩展原有的ResponseConsumer，增加实时考勤事件处理
 * 
 * <AUTHOR>
 * @date 2025-07-01
 */
@Slf4j
public class StudyResponseConsumer extends AsyncCharConsumer<Boolean> {

    private final String deviceIp;
    private final CopyOnWriteArrayList<Character> chBuffer;
    private final StudyAlarmDataParser alarmDataParser;
    private final ScheduledExecutorService executor;
    
    private ScheduledFuture<?> timeoutFuture;
    private int curHttpContentType = 0;
    private String curHttpBoundary = "";
    private final String boundary = "boundary";

    public StudyResponseConsumer(String deviceIp, CopyOnWriteArrayList<Character> chBuffer) {
        this.deviceIp = deviceIp;
        this.chBuffer = chBuffer;
        this.alarmDataParser = new StudyAlarmDataParser();
        this.executor = Executors.newSingleThreadScheduledExecutor();
    }

    @Override
    protected void onResponseReceived(final HttpResponse response) {
        // 确定消息类型
        String responseStr = response.toString();
        log.debug("收到响应: deviceIp={}, response={}", deviceIp, responseStr);
        
        curHttpContentType = getContentType(responseStr);
        
        // 获取实际的boundary信息
        for (org.apache.http.Header headerItem : response.getAllHeaders()) {
            if (headerItem.getName().contains("Content-Type")) {
                String headerValue = headerItem.getValue();
                for (String item : headerValue.split(";")) {
                    if (item.contains(boundary)) {
                        curHttpBoundary = item.split("=")[1];
                    }
                }
            }
        }
        
        // 启动超时任务
        startTimeoutTask();
    }

    @Override
    protected void onCharReceived(final CharBuffer buf, final IOControl ioctrl) throws IOException {
        // 重置超时任务
        cancelTimeout();
        startTimeoutTask();

        StringBuilder strBuilder = new StringBuilder();
        
        // 按照消息类型解析
        switch (curHttpContentType) {
            case 6: { // MULTIPART_FORM_DATA
                for (int i = 0; i < buf.length(); i++) {
                    // 填充缓冲区
                    chBuffer.add(buf.charAt(i));
                    strBuilder.append(buf.charAt(i));
                }
                
                if (strBuilder.toString().contains("--" + curHttpBoundary)) {
                    // 一次表单数据解析
                    parseMultiData();
                }
                break;
            }
            default: {
                log.debug("未匹配到可以解析的content-type: {}", curHttpContentType);
            }
        }
    }

    @Override
    protected void onEntityEnclosed(org.apache.http.HttpEntity entity, org.apache.http.ContentType contentType) throws IOException {
        // 处理实体内容
        log.debug("收到实体内容: deviceIp={}, contentType={}", deviceIp, contentType);
    }

    @Override
    protected Boolean buildResult(HttpContext context) throws Exception {
        log.debug("构建结果: deviceIp={}", deviceIp);
        return true;
    }

    @Override
    protected void releaseResources() {
        // 清理资源
        cancelTimeout();
        if (executor != null && !executor.isShutdown()) {
            executor.shutdown();
        }
        log.debug("释放资源: deviceIp={}", deviceIp);
    }

    /**
     * 启动超时任务
     */
    private void startTimeoutTask() {
        timeoutFuture = executor.schedule(() -> {
            // 30秒未收到心跳或事件，抛出超时异常并关闭链接
            log.warn("设备连接超时: deviceIp={}", deviceIp);
            try {
                this.close();
            } catch (IOException e) {
                log.error("关闭连接失败: deviceIp={}, error={}", deviceIp, e.getMessage());
            }
        }, 30, TimeUnit.SECONDS);
    }

    /**
     * 取消超时任务
     */
    private void cancelTimeout() {
        if (timeoutFuture != null && !timeoutFuture.isCancelled()) {
            timeoutFuture.cancel(false);
        }
    }

    /**
     * 解析多部分数据
     */
    private void parseMultiData() {
        try {
            if (chBuffer.size() < 100) {
                return; // 数据太少，等待更多数据
            }

            // 转换为字符串进行解析
            StringBuilder dataBuilder = new StringBuilder();
            for (Character ch : chBuffer) {
                dataBuilder.append(ch);
            }
            String dataStr = dataBuilder.toString();

            // 查找boundary分隔符
            String boundaryMark = "--" + curHttpBoundary.trim();
            String[] parts = dataStr.split(boundaryMark);

            for (String part : parts) {
                if (part.trim().isEmpty()) {
                    continue;
                }

                // 解析每个部分
                parseDataPart(part);
            }

            // 清空缓冲区
            chBuffer.clear();

        } catch (Exception e) {
            log.error("解析多部分数据失败: deviceIp={}, error={}", deviceIp, e.getMessage());
        }
    }

    /**
     * 解析数据部分
     */
    private void parseDataPart(String dataPart) {
        try {
            // 查找Content-Type
            String contentTypeStr = "";
            if (dataPart.contains("Content-Type:")) {
                int start = dataPart.indexOf("Content-Type:") + 13;
                int end = dataPart.indexOf("\r\n", start);
                if (end > start) {
                    contentTypeStr = dataPart.substring(start, end).trim();
                }
            }

            // 查找数据内容（在\r\n\r\n之后）
            String content = "";
            int dataStart = dataPart.indexOf("\r\n\r\n");
            if (dataStart > 0) {
                content = dataPart.substring(dataStart + 4).trim();
            }

            if (!content.isEmpty()) {
                int contentType = getContentType(contentTypeStr);
                
                // 调用解析器处理事件数据
                alarmDataParser.parseAlarmInfo(
                    contentType, 
                    getStoreFolderPath(), 
                    content, 
                    null, 
                    deviceIp
                );
            }

        } catch (Exception e) {
            log.error("解析数据部分失败: deviceIp={}, error={}", deviceIp, e.getMessage());
        }
    }

    /**
     * 获取内容类型
     */
    private int getContentType(String content) {
        if (content == null) {
            return 0;
        }

        if (content.contains("json")) {
            return 3; // APPLICATION_JSON
        } else if (content.contains("xml")) {
            return 4; // APPLICATION_XML
        } else if (content.contains("jpeg")) {
            return 7; // IMAGE_JPEG
        } else if (content.contains("png")) {
            return 8; // IMAGE_PNG
        } else if (content.contains("multipart")) {
            return 6; // MULTIPART_FORM_DATA
        } else {
            return 0;
        }
    }

    /**
     * 获取存储文件夹路径
     */
    private String getStoreFolderPath() {
        return System.getProperty("user.dir") + "/output/guard/event/";
    }
}
