package com.base.study.service.impl;

import java.util.List;
import com.base.common.utils.DateUtils;
import com.base.common.utils.SecurityUtils;
import com.base.study.domain.StudyAttendance;
import com.base.study.domain.StudyCourse;
import com.base.study.domain.StudyRegistration;
import com.base.study.domain.StudyLeaveRequest;
import com.base.study.mapper.*;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.base.study.service.IStudyLeaveRequestService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 请假申请Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-05-22
 */
@Service
public class StudyLeaveRequestServiceImpl implements IStudyLeaveRequestService 
{
    @Autowired
    private StudyLeaveRequestMapper studyLeaveRequestMapper;

    @Autowired
    private StudyCourseMapper studyCourseMapper;

    @Autowired
    private StudyRegistrationMapper studyRegistrationMapper;

    @Autowired
    private StudyAttendanceMapper studyAttendanceMapper;

    /**
     * 查询请假申请
     * 
     * @param requestId 请假申请主键
     * @return 请假申请
     */
    @Override
    public StudyLeaveRequest selectStudyLeaveRequestById(Long requestId)
    {
        return studyLeaveRequestMapper.selectStudyLeaveRequestById(requestId);
    }

    /**
     * 查询请假申请列表
     * 
     * @param studyLeaveRequest 请假申请
     * @return 请假申请
     */
    @Override
    public List<StudyLeaveRequest> selectStudyLeaveRequestList(StudyLeaveRequest studyLeaveRequest)
    {
        // 如果不是管理员，只查询自己的请假记录
        if (!SecurityUtils.isAdmin(SecurityUtils.getUserId())) {
            studyLeaveRequest.setUserId(SecurityUtils.getUserId());
        }
        return studyLeaveRequestMapper.selectStudyLeaveRequestList(studyLeaveRequest);
    }

    /**
     * 新增请假申请
     * 
     * @param studyLeaveRequest 请假申请
     * @return 结果
     */
    @Override
    public int insertStudyLeaveRequest(StudyLeaveRequest studyLeaveRequest)
    {
        List<StudyLeaveRequest> studyLeaveRequests = studyLeaveRequestMapper.selectStudyLeaveRequestList(studyLeaveRequest);
        if (CollectionUtils.isNotEmpty(studyLeaveRequests)){
            return 0;
        }
        studyLeaveRequest.setUserId(SecurityUtils.getUserId());
        studyLeaveRequest.setCreateTime(DateUtils.getNowDate());
        studyLeaveRequest.setAuditStatus("0"); // 初始状态为待审批
        return studyLeaveRequestMapper.insertStudyLeaveRequest(studyLeaveRequest);
    }

    /**
     * 修改请假申请
     * 
     * @param studyLeaveRequest 请假申请
     * @return 结果
     */
    @Override
    @Transactional
    public int updateStudyLeaveRequest(StudyLeaveRequest studyLeaveRequest)
    {
        studyLeaveRequest.setUpdateTime(DateUtils.getNowDate());
        
        // 1. 检查状态是否为 "批准"
        if ("1".equals(studyLeaveRequest.getAuditStatus())) {
            // 获取请假单的完整信息
            StudyLeaveRequest leaveRequestDetails = studyLeaveRequestMapper.selectStudyLeaveRequestById(studyLeaveRequest.getRequestId());
            if (leaveRequestDetails == null) {
                throw new RuntimeException("请假单不存在");
            }

            // 2. 根据 courseId 获取课程信息
            StudyCourse course = studyCourseMapper.selectStudyCourseByCourseId(leaveRequestDetails.getCourseId());
            if (course == null) {
                throw new RuntimeException("课程信息不存在");
            }

            // 3. 根据 userId 和 classId (从 course 中获取) 找到报名记录
            StudyRegistration registration = studyRegistrationMapper.selectRegistrationByUserAndClass(leaveRequestDetails.getUserId(), course.getClassId());
            if (registration == null) {
                // 如果找不到报名记录，可以根据业务决定是抛出异常还是记录日志后跳过
                throw new RuntimeException("学员报名记录不存在");
            }

            // 4. 创建一条新的考勤记录
            StudyAttendance attendance = new StudyAttendance();
            attendance.setRegistrationId(registration.getRegistrationId());
            attendance.setCourseId(course.getCourseId());
            attendance.setUserId(leaveRequestDetails.getUserId());
            attendance.setCheckInTime(course.getStartTime()); // 将签到时间设置为课程开始时间
            attendance.setAttendanceStatus("请假"); // 状态设置为请假
            attendance.setCreateBy(SecurityUtils.getUsername());
            attendance.setCreateTime(DateUtils.getNowDate());
            attendance.setClassId(course.getClassId());
            studyAttendanceMapper.insertStudyAttendance(attendance);
            
            // 设置审批人信息
            studyLeaveRequest.setApproverId(SecurityUtils.getUserId());
            studyLeaveRequest.setApproveTime(DateUtils.getNowDate());
        }

        // 5. 更新请假申请单的状态
        return studyLeaveRequestMapper.updateStudyLeaveRequest(studyLeaveRequest);
    }

    /**
     * 批量删除请假申请
     * 
     * @param requestIds 需要删除的请假申请主键
     * @return 结果
     */
    @Override
    public int deleteStudyLeaveRequestByRequestIds(Long[] requestIds)
    {
        return studyLeaveRequestMapper.deleteStudyLeaveRequestByRequestIds(requestIds);
    }

    /**
     * 删除请假申请信息
     * 
     * @param requestId 请假申请主键
     * @return 结果
     */
    @Override
    public int deleteStudyLeaveRequestById(Long requestId)
    {
        return studyLeaveRequestMapper.deleteStudyLeaveRequestById(requestId);
    }
}