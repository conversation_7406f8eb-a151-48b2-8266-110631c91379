package com.base.study.service.impl;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.base.common.core.domain.AjaxResult;
import com.base.common.core.domain.entity.SysUser;
import com.base.common.utils.DateUtils;
import com.base.common.utils.SecurityUtils;
import com.base.system.mapper.SysUserMapper;
import com.base.study.domain.StudyAttendance;
import com.base.study.domain.StudyCourse;
import com.base.study.domain.StudyRegistration;
import com.base.study.mapper.StudyCourseMapper;
import com.base.study.mapper.StudyAttendanceMapper;
import com.base.study.mapper.StudyRegistrationMapper;
import com.base.study.service.IStudyAttendanceService;
import org.springframework.util.CollectionUtils;

import static com.base.common.utils.SecurityUtils.getLoginUser;

/**
 * 考勤签到记录表Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-19
 */
@Service
public class StudyAttendanceServiceImpl implements IStudyAttendanceService 
{
    private static final Logger log = LoggerFactory.getLogger(StudyAttendanceServiceImpl.class);

    @Autowired
    private StudyAttendanceMapper studyAttendanceMapper;

    @Resource
    private SysUserMapper userMapper;

    @Resource
    private StudyRegistrationMapper registrationMapper;

    @Resource
    private StudyCourseMapper courseMapper;

    /**
     * 查询考勤签到记录表
     * 
     * @param attendanceId 考勤签到记录表主键
     * @return 考勤签到记录表
     */
    @Override
    public StudyAttendance selectStudyAttendanceByAttendanceId(Long attendanceId)
    {
        return studyAttendanceMapper.selectStudyAttendanceByAttendanceId(attendanceId);
    }

    /**
     * 查询考勤签到记录表列表
     * 
     * @param studyAttendance 考勤签到记录表
     * @return 考勤签到记录表
     */
    @Override
    public List<StudyAttendance> selectStudyAttendanceList(StudyAttendance studyAttendance)
    {
        //根据手机号查询，不是admin的登录手机号查询自己的数据
//        if(!StaticConstant.STRING_ADMIN_PHONE.equals(getLoginUser().getUser().getPhonenumber())){
        if(!SecurityUtils.isAdmin(SecurityUtils.getUserId())){
            // 添加空值检查，避免NullPointerException
            if(studyAttendance.getSysUser() == null) {
                studyAttendance.setSysUser(new SysUser());
            }
            studyAttendance.getSysUser().setPhonenumber(getLoginUser().getUser().getPhonenumber());
        }
        return studyAttendanceMapper.selectStudyAttendanceList(studyAttendance);
    }

    /**
     * 新增考勤签到记录表
     * 
     * @param studyAttendance 考勤签到记录表
     * @return 结果
     */
    @Override
    public AjaxResult insertStudyAttendance(StudyAttendance studyAttendance)
    {
        //如果本次课程已经签到过了，不允许补录信息
        StudyAttendance studyAttendanceQuery = new StudyAttendance();
        studyAttendanceQuery.setClassId(studyAttendance.getClassId());
        studyAttendanceQuery.setUserId(studyAttendance.getUserId());
        studyAttendanceQuery.setCourseId(studyAttendance.getCourseId());
        List<StudyAttendance> studyAttendances = studyAttendanceMapper.selectStudyAttendanceList(studyAttendanceQuery);
        if(!CollectionUtils.isEmpty(studyAttendances) && studyAttendances.size() >= 1){
            return AjaxResult.warn("存在签到记录，不能进行补录");
        }

        //获取报名ID，根据用户ID和班次ID确定唯一
        StudyRegistration studyRegistration = new StudyRegistration();
        //用户ID
        studyRegistration.setUserId(studyAttendance.getUserId());
        //班次ID
        studyRegistration.setClassId(studyAttendance.getClassId());
        //查询报名信息，必然是一条数据
        List<StudyRegistration> studyRegistrations = registrationMapper.selectStudyRegistrationList(studyRegistration);
        //报名信息为空，则没有报名该班次
        if(CollectionUtils.isEmpty(studyRegistrations)){
            return AjaxResult.warn("没用报名该课程，不能进行补录");
        }
        log.info("报名信息--{}",studyRegistrations.get(0));
        if(studyRegistrations.size() > 1){
            //存在第二条数据，则出现问题
            return AjaxResult.warn("查询报名信息错误，请联系管理员");
        }

        studyAttendance.setCreateTime(DateUtils.getNowDate());
        //报名ID
        studyAttendance.setRegistrationId(studyRegistrations.get(0).getRegistrationId());
        //获取课堂信息,根据课堂ID，只有一条数据
        StudyCourse studyCourse = new StudyCourse();
        studyCourse.setCourseId(studyAttendance.getCourseId());
        List<StudyCourse> studyCourses = courseMapper.selectStudyCourseList(studyCourse);
        BeanUtils.copyProperties(studyCourses.get(0),studyCourse);
        //签到时间、签退时间、学分、学时
        studyAttendance.setCheckInTime(studyCourse.getStartTime());
        studyAttendance.setCheckOutTime(studyCourse.getEndTime());
        studyAttendance.setCalculatedCredits(studyCourse.getCredit());
        studyAttendance.setCalculatedHours(studyCourse.getHours());
        //插入数据库
        int rows = studyAttendanceMapper.insertStudyAttendance(studyAttendance);
        if(rows > 0 && studyAttendance.getCheckInTime() != null && studyAttendance.getCheckOutTime() != null) {
            calculateAndSaveAttendanceResult(studyAttendance.getAttendanceId());
        }
        return AjaxResult.success();
    }

    /**
     * 修改考勤签到记录表
     * 
     * @param studyAttendance 考勤签到记录表
     * @return 结果
     */
    @Override
    public int updateStudyAttendance(StudyAttendance studyAttendance)
    {
        studyAttendance.setUpdateTime(DateUtils.getNowDate());
        int rows = studyAttendanceMapper.updateStudyAttendance(studyAttendance);
        if(rows > 0 && studyAttendance.getCheckInTime() != null && studyAttendance.getCheckOutTime() != null) {
            calculateAndSaveAttendanceResult(studyAttendance.getAttendanceId());
        }
        return rows;
    }

    /**
     * 批量删除考勤签到记录表
     * 
     * @param attendanceIds 需要删除的考勤签到记录表主键
     * @return 结果
     */
    @Override
    public int deleteStudyAttendanceByAttendanceIds(Long[] attendanceIds)
    {
        return studyAttendanceMapper.deleteStudyAttendanceByAttendanceIds(attendanceIds);
    }

    /**
     * 删除考勤签到记录表信息
     * 
     * @param attendanceId 考勤签到记录表主键
     * @return 结果
     */
    @Override
    public int deleteStudyAttendanceByAttendanceId(Long attendanceId)
    {
        return studyAttendanceMapper.deleteStudyAttendanceByAttendanceId(attendanceId);
    }

    /**
     * 考勤机打卡
     *
     * @param phonenumber 手机号
     * @param punchTime 打卡时间
     * @return 结果
     * @throws Exception
     */
    @Override
    public AjaxResult punch(String phonenumber, Date punchTime) throws Exception
    {
        // 1. 根据手机号查找用户
        SysUser user = userMapper.selectUserByPhonenumber(phonenumber);
        if (user == null) {
            return AjaxResult.error("手机号未注册");
        }
        Long userId = user.getUserId();

        // 2. 查找该用户当天需要上的、最接近打卡时间的课堂
        // (打卡时间 在 课堂开始时间前30分钟 到 结束时间后30分钟 内)
        List<StudyCourse> todayCourses = courseMapper.selectTodayCoursesByUserId(userId, punchTime);
        if (todayCourses.isEmpty()) {
            return AjaxResult.error("今天没有您的课程");
        }
        // 此处简化处理，取第一门匹配的课程。可根据业务扩展为让用户选择。
        StudyCourse targetCourse = todayCourses.get(0);
        Long courseId = targetCourse.getCourseId();
        Long classId = targetCourse.getClassId();

        // 3. 查找对应的报名记录
        StudyRegistration registration = registrationMapper.selectRegistrationByUserAndClass(userId, classId);
        if (registration == null) {
            return AjaxResult.error("您未报名该课程，无法打卡");
        }
        Long registrationId = registration.getRegistrationId();

        // 4. 查找是否已有签到记录
        StudyAttendance attendance = studyAttendanceMapper.selectAttendanceByUserAndCourse(userId, courseId);

        if (attendance != null) {
            // 已有签到记录，此次为签退
            if(attendance.getCheckOutTime() != null){
                return AjaxResult.error("您今天已完成该课堂的签到和签退，请勿重复操作");
            }
            attendance.setCheckOutTime(punchTime);
            studyAttendanceMapper.updateStudyAttendance(attendance);
            // 触发计算
            calculateAndSaveAttendanceResult(attendance.getAttendanceId());
            return AjaxResult.success("签退成功");

        } else {
            // 没有签到记录，此次为签到
            attendance = new StudyAttendance();
            attendance.setRegistrationId(registrationId);
            attendance.setUserId(userId);
            attendance.setClassId(classId);
            attendance.setCourseId(courseId);
            attendance.setCheckInTime(punchTime);
            attendance.setCheckInSource("0"); // 0-考勤机
            studyAttendanceMapper.insertStudyAttendance(attendance);
             // 触发计算 (仅有签到时间)
            calculateAndSaveAttendanceResult(attendance.getAttendanceId());
            return AjaxResult.success("签到成功");
        }
    }

    /**
     * 计算并保存考勤结果，同时更新报名表的总学时学分
     *
     * @param attendanceId 考勤记录ID
     */
    @Override
    @Transactional
    public void calculateAndSaveAttendanceResult(Long attendanceId) {
        StudyAttendance attendance = studyAttendanceMapper.selectStudyAttendanceById(attendanceId);
        if (attendance == null) return;

        StudyCourse course = courseMapper.selectStudyCourseByCourseId(attendance.getCourseId());
        if (course == null) return;
        
        Date checkInTime = attendance.getCheckInTime();
        Date checkOutTime = attendance.getCheckOutTime();
        Date courseStartTime = course.getStartTime();
        Date courseEndTime = course.getEndTime();

        // 默认有效，学时学分为课堂预设值
        BigDecimal calculatedHours = course.getHours();
        BigDecimal calculatedCredits = course.getCredit();
        String attendanceStatus = "正常";

        boolean isLate = false;
        boolean isEarlyLeave = false;
        
        // 规则1: 必须有签到和签退时间才开始计算
        if(checkInTime == null || checkOutTime == null) {
            attendance.setAttendanceStatus("信息不全");
            attendance.setCalculatedHours(BigDecimal.ZERO);
            attendance.setCalculatedCredits(BigDecimal.ZERO);
            attendance.setCreateBy(SecurityUtils.getUsername());
            studyAttendanceMapper.updateStudyAttendance(attendance);
            // 即使信息不全，也要触发一次总分汇总，以保证数据同步
            updateTotalHoursAndCredits(attendance.getRegistrationId());
            return;
        }

        // 规则2: 迟到或早退超过10分钟，学时学分为0
        long lateMinutes = 0;
        if (checkInTime.after(courseStartTime)) {
            lateMinutes = TimeUnit.MILLISECONDS.toMinutes(checkInTime.getTime() - courseStartTime.getTime());
            if (lateMinutes > 0) isLate = true;
        }

        long earlyLeaveMinutes = 0;
        if (checkOutTime.before(courseEndTime)) {
            earlyLeaveMinutes = TimeUnit.MILLISECONDS.toMinutes(courseEndTime.getTime() - checkOutTime.getTime());
            if(earlyLeaveMinutes > 0) isEarlyLeave = true;
        }

        if (lateMinutes > 10 || earlyLeaveMinutes > 10) {
            calculatedHours = BigDecimal.ZERO;
            calculatedCredits = BigDecimal.ZERO;
            attendanceStatus = "无效";
        } else if(isLate && isEarlyLeave) {
            attendanceStatus = "迟到且早退";
        } else if (isLate) {
            attendanceStatus = "迟到";
        } else if (isEarlyLeave) {
            attendanceStatus = "早退";
        }

        // 更新单次考勤记录的结果
        attendance.setCalculatedHours(calculatedHours);
        attendance.setCalculatedCredits(calculatedCredits);
        attendance.setAttendanceStatus(attendanceStatus);
        attendance.setCreateBy(SecurityUtils.getUsername());
        studyAttendanceMapper.updateStudyAttendance(attendance);

        // 触发总学时学分更新
        updateTotalHoursAndCredits(attendance.getRegistrationId());
    }

    /**
     * 汇总更新报名表的总学时学分
     * @param registrationId
     */
    private void updateTotalHoursAndCredits(Long registrationId) {
        if(registrationId == null) return;
        
        // 重新计算总和
        BigDecimal totalHours = studyAttendanceMapper.sumHoursByRegistrationId(registrationId);
        BigDecimal totalCredits = studyAttendanceMapper.sumCreditsByRegistrationId(registrationId);

        // 更新报名表
        StudyRegistration registration = new StudyRegistration();
        registration.setRegistrationId(registrationId);
        registration.setAchievedHours(totalHours == null ? BigDecimal.ZERO : totalHours);
        registration.setScore(totalCredits == null ? BigDecimal.ZERO : totalCredits);
        registrationMapper.updateStudyRegistration(registration);
    }
} 