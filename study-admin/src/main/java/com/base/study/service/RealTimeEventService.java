package com.base.study.service;

import com.alibaba.fastjson2.JSONObject;
import com.base.study.domain.StudyAttendanceDevice;
import com.base.study.mapper.StudyAttendanceDeviceMapper;
import com.base.common.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Date;
import java.util.List;

/**
 * 实时事件处理服务
 * 处理布防开启后的实时考勤事件
 * 
 * <AUTHOR>
 * @date 2025-07-01
 */
@Slf4j
@Service
public class RealTimeEventService {

    @Autowired
    private DeviceService deviceService;
    
    @Autowired
    private StudyAttendanceDeviceMapper deviceMapper;

    /**
     * 处理实时考勤事件
     * 从布防事件监听中调用
     * 
     * @param deviceIp 设备IP地址
     * @param eventData 事件数据（JSON格式）
     * @return 处理结果
     */
    public boolean processRealTimeAttendanceEvent(String deviceIp, String eventData) {
        try {
            if (StringUtils.isEmpty(deviceIp) || StringUtils.isEmpty(eventData)) {
                log.warn("实时事件处理参数不完整: deviceIp={}, eventData={}", deviceIp, eventData);
                return false;
            }

            // 根据设备IP查找设备信息
            StudyAttendanceDevice device = findDeviceByIp(deviceIp);
            if (device == null) {
                log.warn("未找到设备: IP={}", deviceIp);
                return false;
            }

            // 检查设备是否启用布防
            if (!"1".equals(device.getGuardEnabled())) {
                log.debug("设备[{}]未启用布防，跳过事件处理", device.getDeviceName());
                return false;
            }

            // 解析事件数据
            JSONObject eventJson;
            try {
                eventJson = JSONObject.parseObject(eventData);
            } catch (Exception e) {
                log.error("解析事件数据失败: {}", e.getMessage());
                return false;
            }

            // 检查是否为考勤相关事件
            String eventType = eventJson.getString("eventType");
            if (!isAttendanceEvent(eventType)) {
                log.debug("非考勤事件，跳过处理: eventType={}", eventType);
                return false;
            }

            // 提取考勤信息
            String employeeNo = eventJson.getString("employeeNoString");
            String eventTime = eventJson.getString("time");
            
            if (StringUtils.isEmpty(employeeNo) || StringUtils.isEmpty(eventTime)) {
                log.warn("考勤事件信息不完整: employeeNo={}, eventTime={}", employeeNo, eventTime);
                return false;
            }

            // 转换时间格式
            Date attendanceTime;
            try {
                attendanceTime = DateUtils.parseDate(eventTime, "yyyy-MM-dd'T'HH:mm:ss'+08:00'");
            } catch (Exception e) {
                log.error("时间格式转换失败: eventTime={}, error={}", eventTime, e.getMessage());
                return false;
            }

            // 转换用户ID
            Long userId;
            try {
                userId = Long.parseLong(employeeNo);
            } catch (NumberFormatException e) {
                log.warn("设备用户编号格式错误: {}", employeeNo);
                return false;
            }

            // 调用设备服务处理考勤事件
            boolean result = deviceService.processAttendanceEventForUser(device, userId, attendanceTime, eventType);
            
            if (result) {
                log.info("实时考勤事件处理成功: 设备[{}], 用户[{}], 时间[{}]", 
                    device.getDeviceName(), userId, attendanceTime);
            } else {
                log.warn("实时考勤事件处理失败: 设备[{}], 用户[{}], 时间[{}]", 
                    device.getDeviceName(), userId, attendanceTime);
            }
            
            return result;

        } catch (Exception e) {
            log.error("实时事件处理异常: deviceIp={}, error={}", deviceIp, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 根据设备IP查找设备信息
     */
    private StudyAttendanceDevice findDeviceByIp(String deviceIp) {
        try {
            StudyAttendanceDevice queryParam = new StudyAttendanceDevice();
            queryParam.setDeviceIp(deviceIp);
            queryParam.setStatus("1"); // 启用状态
            List<StudyAttendanceDevice> devices = deviceMapper.selectStudyAttendanceDeviceList(queryParam);
            
            if (devices.isEmpty()) {
                return null;
            }
            
            return devices.get(0);
        } catch (Exception e) {
            log.error("查找设备失败: deviceIp={}, error={}", deviceIp, e.getMessage());
            return null;
        }
    }

    /**
     * 判断是否为考勤相关事件
     */
    private boolean isAttendanceEvent(String eventType) {
        if (StringUtils.isEmpty(eventType)) {
            return false;
        }
        
        // 海康威视考勤相关事件类型
        return eventType.contains("AccessControl") || 
               eventType.contains("CardReader") ||
               eventType.contains("FaceRecognition") ||
               eventType.contains("FingerPrint") ||
               eventType.equals("AcsEvent") ||
               eventType.equals("AccessControllerEvent");
    }

    /**
     * 处理XML格式的事件数据
     * 
     * @param deviceIp 设备IP地址
     * @param xmlEventData XML格式的事件数据
     * @return 处理结果
     */
    public boolean processRealTimeAttendanceEventXml(String deviceIp, String xmlEventData) {
        try {
            // 将XML转换为JSON格式处理
            // 这里可以使用XML解析库，简化起见先记录日志
            log.debug("收到XML格式事件数据: deviceIp={}, data={}", deviceIp, xmlEventData);
            
            // TODO: 如果需要处理XML格式，可以在这里添加XML解析逻辑
            // 目前主要处理JSON格式的事件数据
            
            return false;
        } catch (Exception e) {
            log.error("XML事件处理异常: deviceIp={}, error={}", deviceIp, e.getMessage());
            return false;
        }
    }
}
