package com.base.study.service.impl;

import java.util.Date;
import java.util.List;
import com.base.common.utils.DateUtils;
import com.base.common.utils.SecurityUtils;
import com.base.common.utils.StaticConstant;
import com.base.study.mapper.StudyCourseMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.base.study.domain.StudyCourse;
import com.base.study.service.IStudyCourseService;
import org.springframework.transaction.annotation.Transactional;

import static com.base.common.utils.SecurityUtils.getLoginUser;

/**
 * 课堂信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-05-22
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class StudyCourseServiceImpl implements IStudyCourseService 
{
    private static final Logger log = LoggerFactory.getLogger(StudyCourseServiceImpl.class);

    @Autowired
    private StudyCourseMapper studyCourseMapper;

    /**
     * 查询课堂信息
     * 
     * @param courseId 课堂信息主键
     * @return 课堂信息
     */
    @Override
    public StudyCourse selectStudyCourseByCourseId(Long courseId)
    {
        return studyCourseMapper.selectStudyCourseByCourseId(courseId);
    }

    /**
     * 查询课堂信息列表
     * 
     * @param studyCourse 课堂信息
     * @return 课堂信息
     */
    @Override
    public List<StudyCourse> selectStudyCourseList(StudyCourse studyCourse)
    {
//        //根据手机号查询，不是admin的登录手机号查询自己的数据
//        if(!StaticConstant.STRING_ADMIN_PHONE.equals(getLoginUser().getUser().getPhonenumber())){
        if(!SecurityUtils.isAdmin(SecurityUtils.getUserId())){
            // 通过USERID查询用户下所有课程
            Date nowData = new Date(); // 当前日期和时间
            List<StudyCourse> studyCourses = studyCourseMapper.selectTodayCoursesByUserIdAndStartTime(getLoginUser().getUser().getUserId(),nowData);
            return studyCourses;
        }
        return studyCourseMapper.selectStudyCourseList(studyCourse);
    }

    /**
     * 新增课堂信息
     * 
     * @param studyCourse 课堂信息
     * @return 结果
     */
    @Override
    public int insertStudyCourse(StudyCourse studyCourse)
    {
        //新增之前进行校验，一个班次的课程不能存在冲突
        log.info("班次ID--{}",studyCourse.getClassId());
        StudyCourse studyCourseByDb = new StudyCourse();
        studyCourseByDb.setClassId(studyCourse.getClassId());
        //查询相关的课程信息
        List<StudyCourse> studyCourses = studyCourseMapper.selectStudyCourseList(studyCourseByDb);
        //进行判断，一个班次中的课程不能产生冲突
        int result = courseTimeConflict(studyCourses, studyCourse);
        if(result < 0){
            return result;
        }
        studyCourse.setCreateTime(DateUtils.getNowDate());
        return studyCourseMapper.insertStudyCourse(studyCourse);
    }

    /**
     * 修改课堂信息
     * 
     * @param studyCourse 课堂信息
     * @return 结果
     */
    @Override
    public int updateStudyCourse(StudyCourse studyCourse)
    {
        //新增之前进行校验，一个班次的课程不能存在冲突
        log.info("班次ID--{}",studyCourse.getClassId());
        StudyCourse studyCourseByDb = new StudyCourse();
        studyCourseByDb.setClassId(studyCourse.getClassId());
        //查询相关的课程信息
        List<StudyCourse> studyCourses = studyCourseMapper.selectStudyCourseList(studyCourseByDb);
        //进行判断，一个班次中的课程不能产生冲突
        int result = courseTimeConflict(studyCourses, studyCourse);
        if(result < 0){
            return result;
        }
        studyCourse.setUpdateTime(DateUtils.getNowDate());
        return studyCourseMapper.updateStudyCourse(studyCourse);
    }

    /**
     * 批量删除课堂信息
     * 
     * @param courseIds 需要删除的课堂信息主键
     * @return 结果
     */
    @Override
    public int deleteStudyCourseByCourseIds(Long[] courseIds)
    {
        return studyCourseMapper.deleteStudyCourseByCourseIds(courseIds);
    }

    /**
     * 删除课堂信息信息
     * 
     * @param courseId 课堂信息主键
     * @return 结果
     */
    @Override
    public int deleteStudyCourseByCourseId(Long courseId)
    {
        return studyCourseMapper.deleteStudyCourseByCourseId(courseId);
    }

    /**
     * 对于课程是否存在冲突进行校验
     * @param studyCourses
     * @param studyCourse
     * @return
     */
    public int courseTimeConflict(List<StudyCourse> studyCourses,StudyCourse studyCourse){
        int result = 0;
        //进行判断，一个班次中的课程不能产生冲突
        for (StudyCourse studyCourseByDate:studyCourses) {
            /**
             * 校验时间
             * 1.校验新增课程的开始时间与结束时间是否在其他课程的时间节点内
             * 2.直接返回-3，未新增
             * **/
            if(DateUtils.isInTimeRange(studyCourse.getStartTime(),studyCourseByDate.getStartTime(),studyCourseByDate.getEndTime())
                    || DateUtils.isInTimeRange(studyCourse.getEndTime(),studyCourseByDate.getStartTime(),studyCourseByDate.getEndTime())){
                result = StaticConstant.INT_MINUS_THREE;
            }

        }
        return result;
    }
} 