package com.base.study.service;

import com.base.study.domain.StudyAttendanceDevice;
import com.base.study.guard.StudyResponseConsumer;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.concurrent.FutureCallback;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.apache.http.impl.nio.client.CloseableHttpAsyncClient;
import org.apache.http.impl.nio.client.HttpAsyncClients;
import org.apache.http.nio.client.methods.HttpAsyncMethods;
import org.springframework.stereotype.Service;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.Future;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 学习系统布防服务
 * 管理设备布防状态和实时事件监听
 * 
 * <AUTHOR>
 * @date 2025-07-01
 */
@Slf4j
@Service
public class StudyGuardService {

    /**
     * 设备布防状态管理
     * Key: 设备IP, Value: 布防状态信息
     */
    private final ConcurrentHashMap<String, GuardStatus> deviceGuardStatusMap = new ConcurrentHashMap<>();

    /**
     * 启动设备布防
     * 
     * @param device 考勤设备信息
     * @return 启动结果
     */
    public boolean startGuard(StudyAttendanceDevice device) {
        try {
            String deviceKey = device.getDeviceIp();
            
            // 先停止已有的布防
            stopGuard(device);

            // 创建布防状态
            GuardStatus guardStatus = new GuardStatus();
            guardStatus.stopLink = new AtomicBoolean(false);
            guardStatus.dataRecv = new AtomicBoolean(false);
            guardStatus.device = device;
            
            deviceGuardStatusMap.put(deviceKey, guardStatus);

            // 启动长连接监听线程
            Thread guardThread = new Thread(() -> startLongLinkGuard(device, guardStatus));
            guardThread.setName("Guard-" + device.getDeviceName());
            guardThread.setDaemon(true);
            guardThread.start();

            log.info("设备布防启动成功: {}", device.getDeviceName());
            return true;

        } catch (Exception e) {
            log.error("设备布防启动失败: {}, 错误: {}", device.getDeviceName(), e.getMessage(), e);
            return false;
        }
    }

    /**
     * 停止设备布防
     * 
     * @param device 考勤设备信息
     * @return 停止结果
     */
    public boolean stopGuard(StudyAttendanceDevice device) {
        try {
            String deviceKey = device.getDeviceIp();
            GuardStatus guardStatus = deviceGuardStatusMap.get(deviceKey);
            
            if (guardStatus != null) {
                guardStatus.stopLink.set(true);
                guardStatus.dataRecv.set(false);
                
                // 关闭HTTP客户端
                if (guardStatus.httpClient != null && !guardStatus.httpClient.isRunning()) {
                    guardStatus.httpClient.close();
                }
                
                deviceGuardStatusMap.remove(deviceKey);
                log.info("设备布防停止成功: {}", device.getDeviceName());
            }
            
            return true;

        } catch (Exception e) {
            log.error("设备布防停止失败: {}, 错误: {}", device.getDeviceName(), e.getMessage());
            return false;
        }
    }

    /**
     * 检查设备布防状态
     * 
     * @param device 考勤设备信息
     * @return 布防状态
     */
    public boolean isGuardActive(StudyAttendanceDevice device) {
        String deviceKey = device.getDeviceIp();
        GuardStatus guardStatus = deviceGuardStatusMap.get(deviceKey);
        return guardStatus != null && !guardStatus.stopLink.get();
    }

    /**
     * 获取所有活跃的布防设备数量
     * 
     * @return 活跃设备数量
     */
    public int getActiveGuardCount() {
        return (int) deviceGuardStatusMap.values().stream()
                .filter(status -> !status.stopLink.get())
                .count();
    }

    /**
     * 启动长连接布防监听
     */
    private void startLongLinkGuard(StudyAttendanceDevice device, GuardStatus guardStatus) {
        try {
            // 构建事件监听URL
            String protocol = "http"; // 可以根据需要支持https
            String url = protocol + "://" + device.getDeviceIp() + ":" + 
                        (device.getDevicePort() != null ? device.getDevicePort() : "80") + 
                        "/ISAPI/Event/notification/alertStream";

            log.info("启动设备事件监听: {} -> {}", device.getDeviceName(), url);

            // 初始化HTTP异步客户端
            CredentialsProvider credentialsProvider = new BasicCredentialsProvider();
            credentialsProvider.setCredentials(
                AuthScope.ANY, 
                new UsernamePasswordCredentials(device.getUsername(), device.getPassword())
            );
            
            CloseableHttpAsyncClient httpClient = HttpAsyncClients.custom()
                    .setDefaultCredentialsProvider(credentialsProvider)
                    .build();
            
            guardStatus.httpClient = httpClient;

            // 创建字符缓冲区
            CopyOnWriteArrayList<Character> chBuffer = new CopyOnWriteArrayList<>();

            // 建立回调
            FutureCallback<Boolean> callback = new FutureCallback<Boolean>() {
                @Override
                public void completed(Boolean result) {
                    log.debug("设备事件监听完成: {}, result={}", device.getDeviceName(), result);
                }

                @Override
                public void failed(Exception ex) {
                    log.error("设备事件监听失败: {}, error={}", device.getDeviceName(), ex.getMessage());
                    // 尝试重连
                    if (!guardStatus.stopLink.get()) {
                        scheduleReconnect(device, guardStatus);
                    }
                }

                @Override
                public void cancelled() {
                    log.info("设备事件监听取消: {}", device.getDeviceName());
                }
            };

            // 启动HTTP客户端
            httpClient.start();

            // 执行长连接请求
            Future<Boolean> future = httpClient.execute(
                HttpAsyncMethods.createGet(url),
                new StudyResponseConsumer(device.getDeviceIp(), chBuffer),
                callback
            );

            // 等待结果
            Boolean result = future.get();
            log.info("设备事件监听结果: {}, result={}", device.getDeviceName(), result);

        } catch (Exception e) {
            log.error("长连接布防监听异常: {}, error={}", device.getDeviceName(), e.getMessage(), e);
            
            // 如果不是主动停止，则尝试重连
            if (!guardStatus.stopLink.get()) {
                scheduleReconnect(device, guardStatus);
            }
        }
    }

    /**
     * 调度重连
     */
    private void scheduleReconnect(StudyAttendanceDevice device, GuardStatus guardStatus) {
        try {
            // 等待5秒后重连
            Thread.sleep(5000);
            
            if (!guardStatus.stopLink.get()) {
                log.info("尝试重新连接设备: {}", device.getDeviceName());
                startLongLinkGuard(device, guardStatus);
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.warn("重连调度被中断: {}", device.getDeviceName());
        }
    }

    /**
     * 布防状态信息
     */
    private static class GuardStatus {
        /** 是否停止连接 */
        public AtomicBoolean stopLink = new AtomicBoolean(false);
        /** 是否接收到数据 */
        public AtomicBoolean dataRecv = new AtomicBoolean(false);
        /** 设备信息 */
        public StudyAttendanceDevice device;
        /** HTTP客户端 */
        public CloseableHttpAsyncClient httpClient;
    }
}
