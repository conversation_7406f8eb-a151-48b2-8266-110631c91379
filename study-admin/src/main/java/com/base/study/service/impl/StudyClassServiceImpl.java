package com.base.study.service.impl;

import java.util.ArrayList;
import java.util.List;
import com.base.common.utils.DateUtils;
import com.base.study.domain.StudyCourse;
import com.base.study.mapper.StudyCourseMapper;
import com.base.study.mapper.StudyAttendanceMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.base.study.mapper.StudyClassMapper;
import com.base.study.domain.StudyClass;
import com.base.study.service.IStudyClassService;

/**
 * 党员学习班次表Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-19
 */
@Service
public class StudyClassServiceImpl implements IStudyClassService 
{
    @Autowired
    private StudyClassMapper studyClassMapper;
    @Autowired
    private StudyCourseMapper studyCourseMapper;
    @Autowired
    private StudyAttendanceMapper studyAttendanceMapper;

    /**
     * 查询党员学习班次表
     * 
     * @param classId 党员学习班次表主键
     * @return 党员学习班次表
     */
    @Override
    public StudyClass selectStudyClassByClassId(Long classId)
    {
        //通过classId查询班次信息
        StudyClass studyClass = studyClassMapper.selectStudyClassByClassId(classId);
        //通过班次ID查询课程的学时跟学分总和
        StudyClass studyClassSum = studyClassMapper.selectCreditHoursByClassIds(classId);
        //将学时赋值到实体类
        studyClass.setRequiredHours(studyClassSum.getRequiredHours());
        //将学分赋值到实体类
        studyClass.setCredits(studyClassSum.getCredits());
        return studyClass;
    }

    /**
     * 查询党员学习班次表列表
     * 
     * @param studyClass 党员学习班次表
     * @return 党员学习班次表
     */
    @Override
    public List<StudyClass> selectStudyClassList(StudyClass studyClass)
    {
        //首先查询班次列表
        List<StudyClass> studyClassesList = studyClassMapper.selectStudyClassList(studyClass);
        //循环班次列表
        for (StudyClass studyClasses : studyClassesList) {
            //通过班次ID查询课程的学时跟学分总和
            StudyClass studyClassSum = studyClassMapper.selectCreditHoursByClassIds(studyClasses.getClassId());
            //将学时赋值到实体类
            studyClasses.setRequiredHours(studyClassSum.getRequiredHours());
            //将学分赋值到实体类
            studyClasses.setCredits(studyClassSum.getCredits());
        }
        return studyClassesList;
    }

    /**
     * 新增党员学习班次表
     * 
     * @param studyClass 党员学习班次表
     * @return 结果
     */
    @Override
    public int insertStudyClass(StudyClass studyClass)
    {
        studyClass.setCreateTime(DateUtils.getNowDate());
        return studyClassMapper.insertStudyClass(studyClass);
    }

    /**
     * 修改党员学习班次表
     * 
     * @param studyClass 党员学习班次表
     * @return 结果
     */
    @Override
    public int updateStudyClass(StudyClass studyClass)
    {
        studyClass.setUpdateTime(DateUtils.getNowDate());
        return studyClassMapper.updateStudyClass(studyClass);
    }

    /**
     * 批量删除党员学习班次表
     * 
     * @param classIds 需要删除的党员学习班次表主键
     * @return 结果
     */
    @Override
    public int deleteStudyClassByClassIds(Long[] classIds)
    {
        int i = studyAttendanceMapper.countNumByClassIds(classIds);
        if (i != 0){
            return 0;
        }
        studyCourseMapper.deleteStudyCourseByCourseIds(classIds);
        return studyClassMapper.deleteStudyClassByClassIds(classIds);
    }

    /**
     * 删除党员学习班次表信息
     * 
     * @param classId 党员学习班次表主键
     * @return 结果
     */
    @Override
    public int deleteStudyClassByClassId(Long classId)
    {
        int i = studyAttendanceMapper.countNumByClassId(classId);
        if (i != 0){
            return 0;
        }
        studyCourseMapper.deleteStudyCourseByCourseId(classId);
        return studyClassMapper.deleteStudyClassByClassId(classId);
    }

    @Override
    public List<StudyClass> selectStudyClassAndCourseList(StudyClass studyClass) {
        //首先查询班次列表
        List<StudyClass> studyClassesList = selectStudyClassList(studyClass);
//        //循环班次列表
        List<StudyClass> list = new ArrayList<>();

        //循环班次列表
        for (StudyClass studyClasses : studyClassesList) {
            //处理职级
            String classLevel = studyClasses.getClassLevel();
            if (!"".equals(classLevel)) {
                if ("0".equals(classLevel)) {
                    studyClasses.setClassLevel("实职领导");
                } else if ("1".equals(classLevel)) {
                    studyClasses.setClassLevel("科级待遇干部");
                } else if ("2".equals(classLevel)) {
                    studyClasses.setClassLevel("普通公务员");
                } else if ("3".equals(classLevel)) {
                    studyClasses.setClassLevel("其他公务员");
                }
            }
            //通过班次ID查询课程的学时跟学分总和
            StudyCourse course = new StudyCourse();
            course.setClassId(studyClasses.getClassId());
            List<StudyCourse> studyCourse = studyCourseMapper.selectStudyCourseList(course);
            if (CollectionUtils.isNotEmpty(studyCourse)) {
                for (StudyCourse studyCourse1 : studyCourse) {
                    StudyClass newClass = new StudyClass();
                    BeanUtils.copyProperties(studyClasses, newClass);

                    //将课程名称赋值到实体类
                    newClass.setCourseName(studyCourse1.getCourseName());
                    //将教师姓名赋值到实体类
                    newClass.setTeacherName(studyCourse1.getTeacherName());
                    //将课堂开始时间赋值到实体类
                    newClass.setStartTime(studyCourse1.getStartTime());
                    //将课堂结束时间赋值到实体类
                    newClass.setEndTime(studyCourse1.getEndTime());
                    //将学分赋值到实体类
                    newClass.setCredit(studyCourse1.getCredit());
                    //将学时赋值到实体类
                    newClass.setHours(studyCourse1.getHours());
                    list.add(newClass);
                }
            } else {
                list.add(studyClasses);
            }
        }
        return list;
    }
} 