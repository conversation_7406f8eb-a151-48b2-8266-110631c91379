package com.base.study.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.base.common.annotation.Log;
import com.base.common.core.controller.BaseController;
import com.base.common.core.domain.AjaxResult;
import com.base.common.enums.BusinessType;
import com.base.study.domain.StudyAttendanceDevice;
import com.base.study.service.IStudyAttendanceDeviceService;
import com.base.study.service.DeviceService;
import com.base.common.utils.poi.ExcelUtil;
import com.base.common.core.page.TableDataInfo;
import java.util.Date;

/**
 * 考勤机设备信息表Controller
 * 
 * <AUTHOR>
 * @date 2025-06-19
 */
@RestController
@RequestMapping("/study/device")
public class StudyAttendanceDeviceController extends BaseController
{
    @Autowired
    private IStudyAttendanceDeviceService studyAttendanceDeviceService;
    
    @Autowired
    private DeviceService deviceService;

    /**
     * 查询考勤机设备信息表列表
     */
    @PreAuthorize("@ss.hasPermi('study:device:list')")
    @GetMapping("/list")
    public TableDataInfo list(StudyAttendanceDevice studyAttendanceDevice)
    {
        startPage();
        List<StudyAttendanceDevice> list = studyAttendanceDeviceService.selectStudyAttendanceDeviceList(studyAttendanceDevice);
        return getDataTable(list);
    }

    /**
     * 导出考勤机设备信息表列表
     */
    @PreAuthorize("@ss.hasPermi('study:device:export')")
    @Log(title = "考勤机设备信息表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, StudyAttendanceDevice studyAttendanceDevice)
    {
        List<StudyAttendanceDevice> list = studyAttendanceDeviceService.selectStudyAttendanceDeviceList(studyAttendanceDevice);
        ExcelUtil<StudyAttendanceDevice> util = new ExcelUtil<StudyAttendanceDevice>(StudyAttendanceDevice.class);
        util.exportExcel(response, list, "考勤机设备信息表数据");
    }

    /**
     * 获取考勤机设备信息表详细信息
     */
    @PreAuthorize("@ss.hasPermi('study:device:query')")
    @GetMapping(value = "/{deviceId}")
    public AjaxResult getInfo(@PathVariable("deviceId") Long deviceId)
    {
        return success(studyAttendanceDeviceService.selectStudyAttendanceDeviceByDeviceId(deviceId));
    }

    /**
     * 新增考勤机设备信息表
     */
    @PreAuthorize("@ss.hasPermi('study:device:add')")
    @Log(title = "考勤机设备信息表", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody StudyAttendanceDevice studyAttendanceDevice)
    {
        studyAttendanceDevice.setCreateBy(getUsername());
        return toAjax(studyAttendanceDeviceService.insertStudyAttendanceDevice(studyAttendanceDevice));
    }

    /**
     * 修改考勤机设备信息表
     */
    @PreAuthorize("@ss.hasPermi('study:device:edit')")
    @Log(title = "考勤机设备信息表", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody StudyAttendanceDevice studyAttendanceDevice)
    {
        studyAttendanceDevice.setUpdateBy(getUsername());
        return toAjax(studyAttendanceDeviceService.updateStudyAttendanceDevice(studyAttendanceDevice));
    }

    /**
     * 删除考勤机设备信息表
     */
    @PreAuthorize("@ss.hasPermi('study:device:remove')")
    @Log(title = "考勤机设备信息表", businessType = BusinessType.DELETE)
	@DeleteMapping("/{deviceIds}")
    public AjaxResult remove(@PathVariable Long[] deviceIds)
    {
        return toAjax(studyAttendanceDeviceService.deleteStudyAttendanceDeviceByDeviceIds(deviceIds));
    }
    
    /**
     * 测试设备连接
     */
    @PreAuthorize("@ss.hasPermi('study:device:test')")
    @Log(title = "测试设备连接", businessType = BusinessType.OTHER)
    @PostMapping("/test/{deviceId}")
    public AjaxResult testConnection(@PathVariable Long deviceId)
    {
        StudyAttendanceDevice device = studyAttendanceDeviceService.selectStudyAttendanceDeviceByDeviceId(deviceId);
        if (device == null) {
            return error("设备不存在");
        }
        
        boolean result = deviceService.testConnection(device);
        if (result) {
            return success("设备连接成功");
        } else {
            return error("设备连接失败");
        }
    }
    
    /**
     * 启动设备布防
     */
    @PreAuthorize("@ss.hasPermi('study:device:startGuard')")
    @Log(title = "启动设备布防", businessType = BusinessType.UPDATE)
    @PostMapping("/startGuard/{deviceId}")
    public AjaxResult startGuard(@PathVariable Long deviceId)
    {
        StudyAttendanceDevice device = studyAttendanceDeviceService.selectStudyAttendanceDeviceByDeviceId(deviceId);
        if (device == null) {
            return error("设备不存在");
        }
        
        boolean result = deviceService.startGuard(device);
        if (result) {
            // 更新设备布防状态
            device.setGuardEnabled("1");
            device.setUpdateBy(getUsername());
            studyAttendanceDeviceService.updateStudyAttendanceDevice(device);
            return success("设备布防启动成功");
        } else {
            return error("设备布防启动失败");
        }
    }
    
    /**
     * 关闭设备布防
     */
    @PreAuthorize("@ss.hasPermi('study:device:stopGuard')")
    @Log(title = "关闭设备布防", businessType = BusinessType.UPDATE)
    @PostMapping("/stopGuard/{deviceId}")
    public AjaxResult stopGuard(@PathVariable Long deviceId)
    {
        StudyAttendanceDevice device = studyAttendanceDeviceService.selectStudyAttendanceDeviceByDeviceId(deviceId);
        if (device == null) {
            return error("设备不存在");
        }
        
        boolean result = deviceService.stopGuard(device);
        if (result) {
            // 更新设备布防状态
            device.setGuardEnabled("0");
            device.setUpdateBy(getUsername());
            studyAttendanceDeviceService.updateStudyAttendanceDevice(device);
            return success("设备布防关闭成功");
        } else {
            return error("设备布防关闭失败");
        }
    }
    
    /**
     * 同步班级学员到设备
     */
    @PreAuthorize("@ss.hasPermi('study:device:syncUsers')")
    @Log(title = "同步班级学员", businessType = BusinessType.OTHER)
    @PostMapping("/syncUsers/{classId}")
    public AjaxResult syncClassUsers(@PathVariable Long classId)
    {
        int result = deviceService.syncClassStudentsToDevice(classId);
        if (result > 0) {
            return success("学员同步成功，共同步 " + result + " 人");
        } else {
            return error("学员同步失败或无需同步");
        }
    }
    
    /**
     * 从设备删除班级学员
     */
    @PreAuthorize("@ss.hasPermi('study:device:syncUsers')")
    @Log(title = "删除班级学员", businessType = BusinessType.OTHER)
    @PostMapping("/removeUsers/{classId}")
    public AjaxResult removeClassUsers(@PathVariable Long classId)
    {
        int result = deviceService.removeClassStudentsFromDevice(classId);
        if (result > 0) {
            return success("学员删除成功，共删除 " + result + " 人");
        } else {
            return error("学员删除失败或无需删除");
        }
    }
    
    /**
     * 同步设备考勤记录
     */
    @PreAuthorize("@ss.hasPermi('study:device:syncAttendance')")
    @Log(title = "同步考勤记录", businessType = BusinessType.OTHER)
    @PostMapping("/syncAttendance/{deviceId}")
    public AjaxResult syncAttendanceRecords(@PathVariable Long deviceId)
    {
        StudyAttendanceDevice device = studyAttendanceDeviceService.selectStudyAttendanceDeviceByDeviceId(deviceId);
        if (device == null) {
            return error("设备不存在");
        }
        
        // 同步最近24小时的考勤记录
        Date endTime = new Date();
        Date startTime = new Date(endTime.getTime() - 24 * 60 * 60 * 1000);
        
        int result = deviceService.syncDeviceAttendanceRecords(device, startTime, endTime);
        if (result > 0) {
            return success("考勤记录同步成功，共同步 " + result + " 条记录");
        } else {
            return success("考勤记录同步完成，无新记录");
        }
    }
    
    /**
     * 手动同步所有用户到设备
     */
    @PreAuthorize("@ss.hasPermi('study:device:syncUsers')")
    @Log(title = "同步所有用户到设备", businessType = BusinessType.OTHER)
    @PostMapping("/syncAllUsers/{deviceId}")
    public AjaxResult syncAllUsersToDevice(@PathVariable Long deviceId)
    {
        StudyAttendanceDevice device = studyAttendanceDeviceService.selectStudyAttendanceDeviceByDeviceId(deviceId);
        if (device == null) {
            return error("设备不存在");
        }
        
        int result = deviceService.syncAllUsersToDevice(deviceId);
        if (result > 0) {
            return success("用户同步成功，共同步 " + result + " 人到设备");
        } else {
            return error("用户同步失败或无需同步");
        }
    }
    
    /**
     * 手动清除设备所有用户
     */
    @PreAuthorize("@ss.hasPermi('study:device:removeUsers')")
    @Log(title = "清除设备所有用户", businessType = BusinessType.DELETE)
    @PostMapping("/clearAllUsers/{deviceId}")
    public AjaxResult clearAllUsersFromDevice(@PathVariable Long deviceId)
    {
        StudyAttendanceDevice device = studyAttendanceDeviceService.selectStudyAttendanceDeviceByDeviceId(deviceId);
        if (device == null) {
            return error("设备不存在");
        }
        
        int result = deviceService.clearAllUsersFromDevice(deviceId);
        if (result > 0) {
            return success("设备用户清除成功，共清除 " + result + " 人");
        } else {
            return error("设备用户清除失败或无用户");
        }
    }
    
    /**
     * 手动同步单个用户到设备
     */
    @PreAuthorize("@ss.hasPermi('study:device:syncUsers')")
    @Log(title = "同步单个用户到设备", businessType = BusinessType.OTHER)
    @PostMapping("/syncUser/{deviceId}/{userId}")
    public AjaxResult syncUserToDevice(@PathVariable Long deviceId, @PathVariable Long userId)
    {
        StudyAttendanceDevice device = studyAttendanceDeviceService.selectStudyAttendanceDeviceByDeviceId(deviceId);
        if (device == null) {
            return error("设备不存在");
        }
        
        boolean result = deviceService.syncSingleUserToDevice(deviceId, userId);
        if (result) {
            return success("用户同步成功");
        } else {
            return error("用户同步失败");
        }
    }
    
    /**
     * 手动从设备删除单个用户
     */
    @PreAuthorize("@ss.hasPermi('study:device:removeUsers')")
    @Log(title = "从设备删除单个用户", businessType = BusinessType.DELETE)
    @PostMapping("/removeUser/{deviceId}/{userId}")
    public AjaxResult removeUserFromDevice(@PathVariable Long deviceId, @PathVariable Long userId)
    {
        StudyAttendanceDevice device = studyAttendanceDeviceService.selectStudyAttendanceDeviceByDeviceId(deviceId);
        if (device == null) {
            return error("设备不存在");
        }

        boolean result = deviceService.removeSingleUserFromDevice(deviceId, userId);
        if (result) {
            return success("用户删除成功");
        } else {
            return error("用户删除失败");
        }
    }

    /**
     * 测试人脸图片上传功能
     */
    @PreAuthorize("@ss.hasPermi('study:device:test')")
    @Log(title = "测试人脸图片上传", businessType = BusinessType.OTHER)
    @PostMapping("/testFaceUpload/{deviceId}")
    public AjaxResult testFaceUpload(@PathVariable Long deviceId, @RequestBody(required = false) String testBase64)
    {
        StudyAttendanceDevice device = studyAttendanceDeviceService.selectStudyAttendanceDeviceByDeviceId(deviceId);
        if (device == null) {
            return error("设备不存在");
        }

        // 使用测试用户编号
        String testUserNo = "test_" + System.currentTimeMillis();

        boolean result = deviceService.testFaceUpload(deviceId, testUserNo, testBase64);
        if (result) {
            return success("人脸图片上传测试成功，测试用户编号: " + testUserNo);
        } else {
            return error("人脸图片上传测试失败，请检查设备连接和配置");
        }
    }

    /**
     * 初始化设备权限模板
     */
    @PreAuthorize("@ss.hasPermi('study:device:init')")
    @Log(title = "初始化设备权限模板", businessType = BusinessType.OTHER)
    @PostMapping("/initPermissionTemplates/{deviceId}")
    public AjaxResult initPermissionTemplates(@PathVariable Long deviceId)
    {
        StudyAttendanceDevice device = studyAttendanceDeviceService.selectStudyAttendanceDeviceByDeviceId(deviceId);
        if (device == null) {
            return error("设备不存在");
        }

        boolean result = deviceService.initializeDevicePermissionTemplates(device);
        if (result) {
            return success("设备权限模板初始化成功，用户现在应该可以正常开门");
        } else {
            return error("设备权限模板初始化失败，请检查设备连接和配置");
        }
    }
}