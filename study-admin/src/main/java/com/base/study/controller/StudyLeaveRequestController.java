package com.base.study.controller;

import com.base.common.annotation.Log;
import com.base.common.core.controller.BaseController;
import com.base.common.core.domain.AjaxResult;
import com.base.common.core.page.TableDataInfo;
import com.base.common.enums.BusinessType;
import com.base.common.utils.poi.ExcelUtil;
import com.base.study.domain.StudyLeaveRequest;
import com.base.study.service.IStudyLeaveRequestService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 请假申请Controller
 * 
 * <AUTHOR>
 * @date 2024-05-22
 */
@RestController
@RequestMapping("/study/leaveRequest")
public class StudyLeaveRequestController extends BaseController
{
    @Autowired
    private IStudyLeaveRequestService studyLeaveRequestService;

    /**
     * 查询请假申请列表
     */
    @PreAuthorize("@ss.hasPermi('study:leaveRequest:list')")
    @GetMapping("/list")
    public TableDataInfo list(StudyLeaveRequest studyLeaveRequest)
    {
        startPage();
        List<StudyLeaveRequest> list = studyLeaveRequestService.selectStudyLeaveRequestList(studyLeaveRequest);
        return getDataTable(list);
    }

    /**
     * 导出请假申请列表
     */
    @PreAuthorize("@ss.hasPermi('study:leaveRequest:export')")
    @Log(title = "请假申请", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public AjaxResult export(StudyLeaveRequest studyLeaveRequest)
    {
        List<StudyLeaveRequest> list = studyLeaveRequestService.selectStudyLeaveRequestList(studyLeaveRequest);
        ExcelUtil<StudyLeaveRequest> util = new ExcelUtil<StudyLeaveRequest>(StudyLeaveRequest.class);
        return util.exportExcel(list, "请假申请数据");
    }

    /**
     * 获取请假申请详细信息
     */
    @PreAuthorize("@ss.hasPermi('study:leaveRequest:query')")
    @GetMapping(value = "/{requestId}")
    public AjaxResult getInfo(@PathVariable("requestId") Long requestId)
    {
        return success(studyLeaveRequestService.selectStudyLeaveRequestById(requestId));
    }

    /**
     * 新增请假申请
     */
    @PreAuthorize("@ss.hasPermi('study:leaveRequest:add')")
    @Log(title = "请假申请", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody StudyLeaveRequest studyLeaveRequest)
    {
        studyLeaveRequest.setCreateBy(getUsername());
        int insertStudyLeaveRequest = studyLeaveRequestService.insertStudyLeaveRequest(studyLeaveRequest);
        if (insertStudyLeaveRequest ==0){
            return error("该课程已存在请假记录，请勿重复提交！");
        }
        return success(insertStudyLeaveRequest);
    }

    /**
     * 修改请假申请
     */
    @PreAuthorize("@ss.hasPermi('study:leaveRequest:edit')")
    @Log(title = "请假申请", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody StudyLeaveRequest studyLeaveRequest)
    {
        studyLeaveRequest.setUpdateBy(getUsername());
        return toAjax(studyLeaveRequestService.updateStudyLeaveRequest(studyLeaveRequest));
    }

    /**
     * 删除请假申请
     */
    @PreAuthorize("@ss.hasPermi('study:leaveRequest:remove')")
    @Log(title = "请假申请", businessType = BusinessType.DELETE)
    @DeleteMapping("/{requestIds}")
    public AjaxResult remove(@PathVariable Long[] requestIds)
    {
        return toAjax(studyLeaveRequestService.deleteStudyLeaveRequestByRequestIds(requestIds));
    }
}