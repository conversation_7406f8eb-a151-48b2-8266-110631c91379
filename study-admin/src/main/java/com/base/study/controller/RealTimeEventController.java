package com.base.study.controller;

import com.base.common.annotation.Log;
import com.base.common.core.controller.BaseController;
import com.base.common.core.domain.AjaxResult;
import com.base.common.enums.BusinessType;
import com.base.study.service.RealTimeEventService;
import com.base.study.service.StudyGuardService;
import com.base.study.domain.StudyAttendanceDevice;
import com.base.study.service.impl.StudyAttendanceDeviceServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

/**
 * 实时事件测试控制器
 * 用于测试实时考勤事件处理功能
 * 
 * <AUTHOR>
 * @date 2025-07-01
 */
@RestController
@RequestMapping("/study/realtime")
public class RealTimeEventController extends BaseController {

    @Autowired
    private RealTimeEventService realTimeEventService;
    
    @Autowired
    private StudyGuardService studyGuardService;
    
    @Autowired
    private StudyAttendanceDeviceServiceImpl deviceService;

    /**
     * 测试实时事件处理
     */
    @PreAuthorize("@ss.hasPermi('study:device:test')")
    @Log(title = "测试实时事件", businessType = BusinessType.OTHER)
    @PostMapping("/testEvent")
    public AjaxResult testRealTimeEvent(@RequestParam String deviceIp, @RequestParam String eventData) {
        try {
            boolean result = realTimeEventService.processRealTimeAttendanceEvent(deviceIp, eventData);
            if (result) {
                return success("实时事件处理成功");
            } else {
                return error("实时事件处理失败");
            }
        } catch (Exception e) {
            return error("实时事件处理异常: " + e.getMessage());
        }
    }

    /**
     * 模拟考勤事件
     */
    @PreAuthorize("@ss.hasPermi('study:device:test')")
    @Log(title = "模拟考勤事件", businessType = BusinessType.OTHER)
    @PostMapping("/simulateEvent")
    public AjaxResult simulateAttendanceEvent(
            @RequestParam String deviceIp,
            @RequestParam Long userId,
            @RequestParam(required = false) String eventTime) {
        try {
            // 构造模拟的考勤事件数据
            String currentTime = eventTime != null ? eventTime : 
                java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss'+08:00'"));
            
            String simulatedEventData = String.format(
                "{\"eventType\":\"AccessControllerEvent\",\"employeeNoString\":\"%d\",\"time\":\"%s\",\"name\":\"CardReader\"}",
                userId, currentTime
            );

            boolean result = realTimeEventService.processRealTimeAttendanceEvent(deviceIp, simulatedEventData);
            if (result) {
                return success("模拟考勤事件处理成功");
            } else {
                return error("模拟考勤事件处理失败");
            }
        } catch (Exception e) {
            return error("模拟考勤事件处理异常: " + e.getMessage());
        }
    }

    /**
     * 查看设备布防状态
     */
    @PreAuthorize("@ss.hasPermi('study:device:query')")
    @GetMapping("/guardStatus/{deviceId}")
    public AjaxResult getGuardStatus(@PathVariable Long deviceId) {
        try {
            StudyAttendanceDevice device = deviceService.selectStudyAttendanceDeviceByDeviceId(deviceId);
            if (device == null) {
                return error("设备不存在");
            }

            boolean isActive = studyGuardService.isGuardActive(device);
            String dbStatus = device.getGuardEnabled();
            
            return success()
                .put("deviceName", device.getDeviceName())
                .put("deviceIp", device.getDeviceIp())
                .put("dbGuardEnabled", dbStatus)
                .put("actualGuardActive", isActive)
                .put("status", isActive ? "运行中" : "已停止");
                
        } catch (Exception e) {
            return error("查询布防状态失败: " + e.getMessage());
        }
    }

    /**
     * 查看所有活跃布防设备
     */
    @PreAuthorize("@ss.hasPermi('study:device:query')")
    @GetMapping("/activeGuards")
    public AjaxResult getActiveGuards() {
        try {
            int activeCount = studyGuardService.getActiveGuardCount();
            return success()
                .put("activeGuardCount", activeCount)
                .put("message", "当前有 " + activeCount + " 个设备正在布防");
        } catch (Exception e) {
            return error("查询活跃布防设备失败: " + e.getMessage());
        }
    }

    /**
     * 手动触发布防启动
     */
    @PreAuthorize("@ss.hasPermi('study:device:startGuard')")
    @Log(title = "手动启动布防", businessType = BusinessType.UPDATE)
    @PostMapping("/startGuard/{deviceId}")
    public AjaxResult manualStartGuard(@PathVariable Long deviceId) {
        try {
            StudyAttendanceDevice device = deviceService.selectStudyAttendanceDeviceByDeviceId(deviceId);
            if (device == null) {
                return error("设备不存在");
            }

            boolean result = studyGuardService.startGuard(device);
            if (result) {
                return success("设备布防启动成功，实时事件监听已开始");
            } else {
                return error("设备布防启动失败");
            }
        } catch (Exception e) {
            return error("启动布防失败: " + e.getMessage());
        }
    }

    /**
     * 手动停止布防
     */
    @PreAuthorize("@ss.hasPermi('study:device:stopGuard')")
    @Log(title = "手动停止布防", businessType = BusinessType.UPDATE)
    @PostMapping("/stopGuard/{deviceId}")
    public AjaxResult manualStopGuard(@PathVariable Long deviceId) {
        try {
            StudyAttendanceDevice device = deviceService.selectStudyAttendanceDeviceByDeviceId(deviceId);
            if (device == null) {
                return error("设备不存在");
            }

            boolean result = studyGuardService.stopGuard(device);
            if (result) {
                return success("设备布防停止成功，实时事件监听已停止");
            } else {
                return error("设备布防停止失败");
            }
        } catch (Exception e) {
            return error("停止布防失败: " + e.getMessage());
        }
    }

    /**
     * 获取实时同步统计信息
     */
    @PreAuthorize("@ss.hasPermi('study:device:query')")
    @GetMapping("/syncStats")
    public AjaxResult getSyncStats() {
        try {
            // 这里可以添加统计信息，比如今日实时同步次数等
            return success()
                .put("message", "实时同步功能已启用")
                .put("description", "布防开启后，考勤事件将实时同步到服务器")
                .put("activeGuards", studyGuardService.getActiveGuardCount());
        } catch (Exception e) {
            return error("获取统计信息失败: " + e.getMessage());
        }
    }
}
