# 实时考勤同步功能说明

## 概述

本系统已实现布防开启后的实时考勤记录同步功能。当设备布防启用时，系统会建立长连接监听设备事件，实时接收并处理考勤打卡记录，立即同步到服务器数据库。

## 核心功能

### 1. 实时事件监听
- **长连接监听**：布防开启后建立与设备的长连接
- **事件实时接收**：监听设备的考勤事件（刷卡、人脸识别等）
- **自动重连机制**：连接断开后自动尝试重连

### 2. 实时数据同步
- **即时处理**：收到考勤事件后立即处理并同步到数据库
- **重复检查**：避免5分钟内的重复考勤记录
- **课程匹配**：自动匹配用户当前时间段的课程

### 3. 布防管理
- **智能布防**：支持手动和自动布防启动
- **状态监控**：实时监控布防状态和连接状态
- **批量管理**：支持多设备同时布防

## 系统架构

```
考勤设备 → 长连接事件流 → StudyResponseConsumer → StudyAlarmDataParser → RealTimeEventService → DeviceService → 数据库
```

### 核心组件

1. **StudyGuardService**：布防管理服务
   - 管理设备布防状态
   - 建立和维护长连接
   - 处理连接异常和重连

2. **StudyResponseConsumer**：事件响应消费者
   - 接收设备事件流数据
   - 解析多部分数据格式
   - 处理超时和异常

3. **StudyAlarmDataParser**：事件数据解析器
   - 解析JSON/XML格式事件数据
   - 提取考勤相关信息
   - 调用实时处理服务

4. **RealTimeEventService**：实时事件处理服务
   - 验证设备和事件信息
   - 过滤考勤相关事件
   - 调用考勤处理逻辑

5. **DeviceService**：设备服务（扩展）
   - 实时考勤事件处理
   - 重复记录检查
   - 课程匹配和数据入库

## 使用方法

### 1. 启动布防

#### 手动启动
```http
POST /study/device/startGuard/{deviceId}
```

#### 自动启动
- 系统会在课程开始前自动启动相关设备的布防
- 定时任务每小时检查并启动应该布防的设备

### 2. 停止布防

#### 手动停止
```http
POST /study/device/stopGuard/{deviceId}
```

#### 自动停止
- 课程结束后系统会自动停止布防

### 3. 监控布防状态

#### 查看单个设备状态
```http
GET /study/realtime/guardStatus/{deviceId}
```

#### 查看所有活跃布防
```http
GET /study/realtime/activeGuards
```

### 4. 测试功能

#### 模拟考勤事件
```http
POST /study/realtime/simulateEvent
参数：
- deviceIp: 设备IP地址
- userId: 用户ID
- eventTime: 事件时间（可选）
```

## 配置要求

### 1. 设备配置
- 设备必须支持ISAPI协议
- 需要配置正确的IP地址、端口、用户名、密码
- 设备状态必须为"启用"

### 2. 网络配置
- 确保服务器能够访问设备的事件监听接口
- 默认使用HTTP协议，端口通常为80
- 事件监听URL：`/ISAPI/Event/notification/alertStream`

### 3. 数据库配置
- 设备表需要包含布防状态字段：`guard_enabled`
- 考勤表需要支持来源标识：`check_in_source`（0=考勤机，1=手动）

## 工作流程

### 1. 布防启动流程
1. 检查设备连接状态
2. 启动StudyGuardService布防
3. 建立长连接到设备事件接口
4. 启动StudyResponseConsumer监听
5. 更新设备布防状态

### 2. 事件处理流程
1. 接收设备事件数据
2. 解析事件内容（JSON/XML）
3. 验证是否为考勤事件
4. 提取用户ID和时间信息
5. 检查重复记录
6. 匹配用户课程
7. 创建考勤记录
8. 保存到数据库

### 3. 异常处理流程
1. 连接超时：自动重连
2. 数据解析失败：记录日志，跳过
3. 用户不存在：记录警告
4. 课程不匹配：记录警告
5. 重复记录：跳过处理

## 性能特点

### 1. 实时性
- 事件接收延迟：< 1秒
- 数据库同步延迟：< 2秒
- 总体响应时间：< 3秒

### 2. 可靠性
- 自动重连机制
- 重复记录检查
- 异常恢复能力
- 定时补充同步

### 3. 扩展性
- 支持多设备并发布防
- 支持不同类型的考勤事件
- 支持JSON和XML数据格式

## 监控和维护

### 1. 日志监控
- 布防启动/停止日志
- 事件接收和处理日志
- 异常和错误日志
- 性能统计日志

### 2. 状态检查
- 定期检查布防状态
- 监控连接健康状况
- 统计同步成功率

### 3. 故障排除
- 检查设备网络连接
- 验证设备配置信息
- 查看事件监听日志
- 检查数据库同步状态

## 注意事项

1. **网络稳定性**：确保服务器与设备之间网络稳定
2. **设备兼容性**：确认设备支持ISAPI事件监听
3. **并发限制**：避免同时启动过多设备布防
4. **资源管理**：定期清理长连接资源
5. **数据一致性**：定时同步作为实时同步的补充

## 升级说明

本次升级主要增加了以下功能：
- 实时事件监听和处理
- 布防状态管理
- 自动重连机制
- 重复记录检查
- 测试和监控接口

原有的定时同步功能保持不变，作为实时同步的补充机制。
