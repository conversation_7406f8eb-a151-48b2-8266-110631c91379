<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.base.study.mapper.StudyCourseMapper">
    
    <resultMap type="com.base.study.domain.StudyCourse" id="StudyCourseResult">
        <result property="courseId"    column="course_id"    />
        <result property="classId"     column="class_id"     />
        <result property="courseName"  column="course_name"  />
        <result property="teacherName" column="teacher_name" />
        <result property="startTime"   column="start_time"   />
        <result property="endTime"     column="end_time"     />
        <result property="credit"      column="credit"       />
        <result property="hours"       column="hours"        />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"  column="create_time"  />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"  column="update_time"  />
        <result property="remark"      column="remark"       />
    </resultMap>

    <sql id="selectStudyCourseVo">
        select course_id, class_id, course_name, teacher_name, start_time, end_time, credit, hours, create_by, create_time, update_by, update_time, remark from study_course
    </sql>

    <select id="selectStudyCourseList" parameterType="com.base.study.domain.StudyCourse" resultMap="StudyCourseResult">
        <include refid="selectStudyCourseVo"/>
        <where>  
            <if test="classId != null "> and class_id = #{classId}</if>
            <if test="courseId != null "> and course_id = #{courseId}</if>
            <if test="courseName != null  and courseName != ''"> and course_name like concat('%', #{courseName}, '%')</if>
            <if test="teacherName != null  and teacherName != ''"> and teacher_name like concat('%', #{teacherName}, '%')</if>
            <if test="params.beginStartTime != null and params.beginStartTime != '' and params.endStartTime != null and params.endStartTime != ''">
                and start_time between #{params.beginStartTime} and #{params.endStartTime}
            </if>
        </where>
    </select>
    
    <select id="selectStudyCourseByCourseId" parameterType="Long" resultMap="StudyCourseResult">
        <include refid="selectStudyCourseVo"/>
        where course_id = #{courseId}
    </select>
        
    <insert id="insertStudyCourse" parameterType="com.base.study.domain.StudyCourse" useGeneratedKeys="true" keyProperty="courseId">
        insert into study_course
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="classId != null">class_id,</if>
            <if test="courseName != null">course_name,</if>
            <if test="teacherName != null">teacher_name,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="credit != null">credit,</if>
            <if test="hours != null">hours,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="classId != null">#{classId},</if>
            <if test="courseName != null">#{courseName},</if>
            <if test="teacherName != null">#{teacherName},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="credit != null">#{credit},</if>
            <if test="hours != null">#{hours},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateStudyCourse" parameterType="com.base.study.domain.StudyCourse">
        update study_course
        <trim prefix="SET" suffixOverrides=",">
            <if test="classId != null">class_id = #{classId},</if>
            <if test="courseName != null">course_name = #{courseName},</if>
            <if test="teacherName != null">teacher_name = #{teacherName},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="credit != null">credit = #{credit},</if>
            <if test="hours != null">hours = #{hours},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where course_id = #{courseId}
    </update>

    <delete id="deleteStudyCourseByCourseId" parameterType="Long">
        delete from study_course where course_id = #{courseId}
    </delete>

    <delete id="deleteStudyCourseByCourseIds" parameterType="String">
        delete from study_course where course_id in 
        <foreach item="courseId" collection="array" open="(" separator="," close=")">
            #{courseId}
        </foreach>
    </delete>

    <select id="selectTodayCoursesByUserId" resultMap="StudyCourseResult">
        select c.* from study_course c
        left join study_registration r on c.class_id = r.class_id
        where r.user_id = #{userId}
          and r.status = '1' -- 已报名
        <if test="punchTime != null">
          and #{punchTime} between date_sub(c.start_time, interval 30 minute) and date_add(c.end_time, interval 30 minute)
        </if>
        order by c.start_time asc
    </select>

    <select id="selectTodayCoursesByUserIdAndStartTime" resultMap="StudyCourseResult">
        select c.* from study_course c
        left join study_registration r on c.class_id = r.class_id
        where r.user_id = #{userId}
        <if test="nowData != null">
            and c.start_time > #{nowData}
        </if>
        order by c.start_time asc
    </select>
</mapper> 