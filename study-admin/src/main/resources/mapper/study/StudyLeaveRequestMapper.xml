<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.base.study.mapper.StudyLeaveRequestMapper">
    
    <resultMap type="com.base.study.domain.StudyLeaveRequest" id="StudyLeaveRequestResult">
        <result property="requestId"       column="request_id"       />
        <result property="userId"          column="user_id"          />
        <result property="courseId"        column="course_id"        />
        <result property="reason"          column="reason"           />
        <result property="hours"           column="hours"            />
        <result property="attachmentUrl"   column="attachment_url"   />
        <result property="auditStatus"     column="audit_status"           />
        <result property="auditBy"      column="audit_by"      />
        <result property="auditTime"     column="audit_time"     />
        <result property="createBy"        column="create_by"        />
        <result property="createTime"      column="create_time"      />
        <result property="updateBy"        column="update_by"        />
        <result property="updateTime"      column="update_time"      />
        <result property="remark"          column="remark"           />
        <result property="userName"        column="user_name"        />
        <result property="courseName"      column="course_name"      />
        <result property="startTime"      column="start_time"      />
        <result property="endTime"      column="end_time"      />
        <result property="endTime"      column="end_time"      />
        <result property="nickName"      column="nick_name"      />
        <result property="phonenumber"      column="phonenumber"      />
        <result property="courseId"      column="course_id"      />
    </resultMap>

    <sql id="selectStudyLeaveRequestVo">
        select slr.request_id, slr.user_id, slr.course_id, slr.reason, slr.hours, slr.attachment_url, slr.audit_status, slr.audit_by, slr.audit_time, slr.create_by, slr.create_time, su.user_name, sc.course_name,su.nick_name,su.phonenumber
        from study_leave_request slr
        left join sys_user su on slr.user_id = su.user_id
        left join study_course sc on slr.course_id = sc.course_id
    </sql>

    <select id="selectStudyLeaveRequestList" parameterType="com.base.study.domain.StudyLeaveRequest" resultMap="StudyLeaveRequestResult">
        <include refid="selectStudyLeaveRequestVo"/>
        <where>
            <if test="userName != null  and userName != ''"> and su.user_name like concat('%', #{userName}, '%')</if>
            <if test="courseName != null  and courseName != ''"> and sc.course_name like concat('%', #{courseName}, '%')</if>
            <if test="auditStatus != null  and auditStatus != ''"> and slr.audit_status = #{auditStatus}</if>
            <if test="userId != null  and userId != ''"> and slr.user_id = #{userId}</if>
            <if test="courseId != null  and courseId != ''"> and slr.course_id = #{courseId}</if>
        </where>
    </select>
    
    <select id="selectStudyLeaveRequestById" parameterType="Long" resultMap="StudyLeaveRequestResult">
        <include refid="selectStudyLeaveRequestVo"/>
        where slr.request_id = #{requestId}
    </select>
        
    <insert id="insertStudyLeaveRequest" parameterType="com.base.study.domain.StudyLeaveRequest" useGeneratedKeys="true" keyProperty="requestId">
        insert into study_leave_request (
            <if test="userId != null">user_id,</if>
            <if test="courseId != null">course_id,</if>
            <if test="reason != null and reason != ''">reason,</if>
            <if test="hours != null">hours,</if>
            <if test="attachmentUrl != null and attachmentUrl != ''">attachment_url,</if>
            <if test="auditStatus != null and auditStatus != ''">audit_status,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            create_time
        ) values (
            <if test="userId != null">#{userId},</if>
            <if test="courseId != null">#{courseId},</if>
            <if test="reason != null and reason != ''">#{reason},</if>
            <if test="hours != null">#{hours},</if>
            <if test="attachmentUrl != null and attachmentUrl != ''">#{attachmentUrl},</if>
            <if test="auditStatus != null and auditStatus != ''">#{auditStatus},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            sysdate()
        )
    </insert>

    <update id="updateStudyLeaveRequest" parameterType="com.base.study.domain.StudyLeaveRequest">
        update study_leave_request
        <set>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="courseId != null">course_id = #{courseId},</if>
            <if test="reason != null and reason != ''">reason = #{reason},</if>
            <if test="hours != null">hours = #{hours},</if>
            <if test="attachmentUrl != null and attachmentUrl != ''">attachment_url = #{attachmentUrl},</if>
            <if test="auditStatus != null and auditStatus != ''">audit_status = #{auditStatus},</if>
            <if test="auditBy != null">audit_by = #{auditBy},</if>
            <if test="auditTime != null">audit_time = #{auditTime},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            update_time = sysdate()
        </set>
        where request_id = #{requestId}
    </update>

    <delete id="deleteStudyLeaveRequestById" parameterType="Long">
        delete from study_leave_request where request_id = #{requestId}
    </delete>

    <delete id="deleteStudyLeaveRequestByRequestIds" parameterType="String">
        delete from study_leave_request where request_id in
        <foreach item="requestId" collection="array" open="(" separator="," close=")">
            #{requestId}
        </foreach>
    </delete>
</mapper> 