# 数据源配置
spring:
    datasource:
        type: com.alibaba.druid.pool.DruidDataSource
        driverClassName: com.mysql.cj.jdbc.Driver
        druid:
            # 主库数据源
            master:
                url: *************************************************************************************************************************************************************************************************
                username: base
                password: base_123
            # 从库数据源
            slave:
                # 从数据源开关/默认关闭
                enabled: false
                url: 
                username: 
                password: 
            # 初始连接数
            initialSize: 5
            # 最小连接池数量
            minIdle: 10
            # 最大连接池数量
            maxActive: 20
            # 配置获取连接等待超时的时间
            maxWait: 60000
            # 配置连接超时时间
            connectTimeout: 30000
            # 配置网络超时时间
            socketTimeout: 60000
            # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
            timeBetweenEvictionRunsMillis: 60000
            # 配置一个连接在池中最小生存的时间，单位是毫秒
            minEvictableIdleTimeMillis: 300000
            # 配置一个连接在池中最大生存的时间，单位是毫秒
            maxEvictableIdleTimeMillis: 900000
            # 配置检测连接是否有效
            validationQuery: SELECT 1 FROM DUAL
            testWhileIdle: true
            testOnBorrow: false
            testOnReturn: false
            webStatFilter: 
                enabled: true
            statViewServlet:
                enabled: true
                # 设置白名单，不填则允许所有访问
                allow:
                url-pattern: /druid/*
                # 控制台管理用户名和密码
                login-username: base
                login-password: 123456
            # 开启SQL日志记录
            log:
                statement:
                    enable: true # 启用SQL语句记录
                    # 你还可以设置以下选项来进一步定制日志输出
                    # statement-log-separator: ";" # SQL语句之间的分隔符
                    # statement-log-max-length: 2048 # SQL语句的最大长度

            # 如果你想要看到SQL语句的参数值，需要配置以下选项
            # 注意：这可能会暴露敏感信息，所以请谨慎使用
            filter:
                slf4j:
                    enabled: false
                    statement-log-enabled: true
                    statement-create-after-log-enabled: false
                    statement-close-after-log-enabled: false
                    result-set-log-enabled: false
                    statement-executable-sql-log-enable: true # 这个选项将打印出带有参数的SQL语句
                stat:
                    enabled: false
                    # 慢SQL记录
                    log-slow-sql: true
                    slow-sql-millis: 1000
                    merge-sql: true
                wall:
                    config:
                        multi-statement-allow: true
