const app = getApp()

Page({
  data: {
    classList: [],
    myRegistrations: [],
    loading: false,
    activeTab: 0, // 0: 可报名班次, 1: 我的报名
    userInfo: {},
    hasLoaded: false // 标记是否已经加载过
  },

  onLoad() {
    this.loadUserInfo()
    // 初始化时需要先加载我的报名数据，再加载可报名班次
    this.initializeData()
    this.setData({ hasLoaded: true })
  },

  // 初始化数据 - 按顺序加载，确保重复报名检查正常工作
  async initializeData() {
    try {
      // 先加载我的报名数据
      await this.loadMyRegistrations()
      // 再加载可报名班次，这样可以正确检查重复报名
      await this.loadAvailableClasses()
    } catch (error) {
      console.error('数据初始化失败:', error)
    }
  },

  onShow() {
    // 只有在已经加载过的情况下才刷新数据
    if (this.data.hasLoaded) {
      if (this.data.activeTab === 0) {
        this.loadAvailableClasses()
      } else {
        this.loadMyRegistrations()
      }
    }
  },

  // 加载用户信息
  loadUserInfo() {
    const userInfo = wx.getStorageSync('userInfo')
    if (userInfo) {
      this.setData({ userInfo })
    } else {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      })
      setTimeout(() => {
        wx.reLaunch({
          url: '/pages/login/login'
        })
      }, 1500)
    }
  },

  // 切换标签页
  switchTab(e) {
    const dataset = e.currentTarget.dataset
    const index = parseInt(dataset.index)
    const currentTab = this.data.activeTab
    
    // 如果点击的是当前标签页，直接返回
    if (currentTab === index) {
      return
    }
    
    // 先更新标签页状态
    this.setData({ 
      activeTab: index,
      loading: false
    })
    
    // 根据标签页加载对应数据
    if (index === 0) {
      this.loadAvailableClasses()
    } else if (index === 1) {
      this.loadMyRegistrations().then(() => {
        // 加载完我的报名后，如果当前是可报名班次页，需要刷新状态
        if (this.data.activeTab === 0) {
          this.loadAvailableClasses()
        }
      })
    }
  },

  // 加载可报名班次
  async loadAvailableClasses() {
    this.setData({ loading: true })
    
    try {
      const res = await app.request({
        url: '/study/class/list',
        data: {
          pageNum: 1,
          pageSize: 50
        }
      })

      if (res && res.rows) {
        const classList = res.rows.map(item => ({
          ...item,
          canRegister: this.canRegister(item),
          statusText: this.getClassStatusText(item)
        }))
        
        this.setData({ classList })
      } else {
        this.setData({ classList: [] })
      }
    } catch (error) {
      console.error('加载班次列表失败:', error)
      wx.showToast({
        title: '加载班次失败',
        icon: 'none'
      })
      this.setData({ classList: [] })
    } finally {
      this.setData({ loading: false })
    }
  },

  // 加载我的报名
  async loadMyRegistrations() {
    const userInfo = this.data.userInfo
    if (!userInfo || !userInfo.userId) {
      wx.showToast({
        title: '用户信息异常',
        icon: 'none'
      })
      return
    }

    this.setData({ loading: true })
    
    try {
      const res = await app.request({
        url: '/study/registration/list',
        data: {
          pageNum: 1,
          pageSize: 50,
          userId: userInfo.userId
        }
      })

      if (res && res.rows) {
        // 为每个报名记录获取完整的班级信息
        const myRegistrations = await Promise.all(res.rows.map(async (item) => {
          try {
            // 获取完整的班级信息
            const classRes = await app.request({
              url: '/study/class/list',
              data: {
                classId: item.classId
              }
            })

            let fullClassInfo = null
            if (classRes && classRes.rows && classRes.rows.length > 0) {
              fullClassInfo = classRes.rows[0]
            }

            return {
              ...item,
              // statusText: this.getRegistrationStatusText(item.status),
              // statusClass: this.getRegistrationStatusClass(item.status),
              className: item.studyClass?.className || fullClassInfo?.className || '未知班次',
              // 补充完整的班级信息
              fullClassInfo: fullClassInfo,
              displayStartDate: fullClassInfo?.classStartDate || '待定',
              displayEndDate: fullClassInfo?.classEndDate || '待定',
              // 格式化数据显示
              achievedHours: item.achievedHours || 0,
              completionRate: item.completionRate ? parseFloat(item.completionRate).toFixed(1) : 0,
              score: item.score ? parseFloat(item.score).toFixed(1) : null
            }
          } catch (error) {
            console.error('获取班级信息失败:', error)
            return {
              ...item,
              // statusText: this.getRegistrationStatusText(item.status),
              // statusClass: this.getRegistrationStatusClass(item.status),
              className: item.studyClass?.className || '未知班次',
              displayStartDate: '获取失败',
              displayEndDate: '获取失败',
              // 格式化数据显示
              achievedHours: item.achievedHours || 0,
              completionRate: item.completionRate ? parseFloat(item.completionRate).toFixed(1) : 0,
              score: item.score ? parseFloat(item.score).toFixed(1) : null
            }
          }
        }))
        
        this.setData({ myRegistrations })
      } else {
        this.setData({ myRegistrations: [] })
      }
    } catch (error) {
      console.error('加载我的报名失败:', error)
      wx.showToast({
        title: '加载报名记录失败',
        icon: 'none'
      })
      this.setData({ myRegistrations: [] })
    } finally {
      this.setData({ loading: false })
    }
  },

  // 判断是否可以报名
  canRegister(classItem) {
    // 首先检查时间条件
    const now = new Date()
    const enrollStart = new Date(classItem.enrollStartDate)
    const enrollEnd = new Date(classItem.enrollEndDate)
    
    const timeAllowed = now >= enrollStart && now <= enrollEnd
    
    // 如果时间不允许，直接返回false
    if (!timeAllowed) {
      return false
    }
    
    // 检查是否已经报名了该班次
    const myRegistrations = this.data.myRegistrations || []
    const alreadyRegistered = myRegistrations.some(registration => 
      registration.classId === classItem.classId
    )
    
    return !alreadyRegistered
  },

  // 获取班次状态文字
  getClassStatusText(classItem) {
    const now = new Date()
    const enrollStart = new Date(classItem.enrollStartDate)
    const enrollEnd = new Date(classItem.enrollEndDate)
    const classStart = new Date(classItem.classStartDate)
    const classEnd = new Date(classItem.classEndDate)
    
    // 检查是否已经报名
    const myRegistrations = this.data.myRegistrations || []
    const alreadyRegistered = myRegistrations.some(registration => 
      registration.classId === classItem.classId
    )
    
    if (alreadyRegistered) {
      return '已报名'
    } else if (now < enrollStart) {
      return '报名未开始'
    } else if (now > enrollEnd && now < classStart) {
      return '报名已结束'
    } else if (now >= classStart && now <= classEnd) {
      return '培训中'
    } else if (now > classEnd) {
      return '已结束'
    } else {
      return '可报名'
    }
  },

  // 获取报名状态文字
  getRegistrationStatusText(status) {
    const statusMap = {
      '0': '待审核',
      '1': '已通过',
      '2': '已拒绝',
      '3': '学习中',
      '4': '已结业',
      '5': '已退班'
    }
    return statusMap[status] || '未知状态'
  },

  // 获取报名状态样式类
  getRegistrationStatusClass(status) {
    const classMap = {
      '0': 'status-warning', // 待审核 - 橙色
      '1': 'status-success', // 已通过 - 绿色
      '2': 'status-danger',  // 已拒绝 - 红色
      '3': 'status-info',    // 学习中 - 蓝色
      '4': 'status-success', // 已结业 - 绿色
      '5': 'status-danger'   // 已退班 - 红色
    }
    return classMap[status] || 'status-info'
  },

  // 立即报名
  async handleRegister(e) {
    const { classItem } = e.currentTarget.dataset
    if (!classItem || !classItem.classId) {
      wx.showToast({ title: '班级信息异常', icon: 'none' })
      return
    }
    const userInfo = this.data.userInfo
    if (!userInfo || !userInfo.userId) {
      wx.showToast({ title: '请先登录', icon: 'none' })
      return
    }
    
    // 前端重复报名检查
    const myRegistrations = this.data.myRegistrations || []
    const alreadyRegistered = myRegistrations.some(registration => 
      registration.classId === classItem.classId
    )
    
    if (alreadyRegistered) {
      wx.showToast({ title: '您已报名该班次', icon: 'none' })
      return
    }
    
    const classId = Number(classItem.classId)
    const userId = Number(userInfo.userId)
    if (isNaN(classId) || isNaN(userId)) {
      wx.showToast({ title: '参数类型错误', icon: 'none' })
      return
    }
    wx.showLoading({ title: '报名中...' })
    try {
      await app.request({
        url: '/study/registration/add',
        method: 'POST',
        data: {
          classId,
          userId,
          status: '0' // 待审核
        }
      })
      wx.showToast({ title: '报名成功', icon: 'success' })
      // 先加载我的报名数据，再刷新可报名班次状态
      await this.loadMyRegistrations()
      this.loadAvailableClasses()
    } catch (error) {
      // error就是app.request中reject的错误信息，即后端返回的msg
      wx.showToast({ title: error || '报名失败', icon: 'none' })
    } finally {
      wx.hideLoading()
    }
  },

  // 查看班次详情
  viewClassDetail(e) {
    const { classItem } = e.currentTarget.dataset
    
    let contentLines = []
    
    // 添加状态信息
    contentLines.push(`🏷️ 当前状态：${classItem.statusText}`)
    
    // 如果不可报名，添加原因说明
    if (!classItem.canRegister) {
      const now = new Date()
      const enrollStart = new Date(classItem.enrollStartDate)
      const enrollEnd = new Date(classItem.enrollEndDate)
      
      if (now < enrollStart) {
        const diffDays = Math.ceil((enrollStart - now) / (1000 * 60 * 60 * 24))
        contentLines.push(`   报名将在${diffDays}天后开始`)
      } else if (now > enrollEnd) {
        contentLines.push(`   报名已于${classItem.enrollEndDate}结束`)
      }
    }
    
    // contentLines.push('')
    //
    // // 时间信息 - 优化换行显示
    // contentLines.push(`📅 报名时间`)
    // contentLines.push(`   开始：${classItem.enrollStartDate}`)
    // contentLines.push(`   结束：${classItem.enrollEndDate}`)
    // contentLines.push('')
    //
    // contentLines.push(`🎓 培训时间`)
    // contentLines.push(`   开始：${classItem.classStartDate}`)
    // contentLines.push(`   结束：${classItem.classEndDate}`)
    // contentLines.push('')
    //
    // // 班次要求
    // if (classItem.requiredHours) {
    //   contentLines.push(`⭐ 总学时：${classItem.requiredHours}小时`)
    // }
    // if (classItem.credits) {
    //   contentLines.push(`🏆 总学分：${classItem.credits}分`)
    // }
    //
    // // 添加空行分隔
    // if (classItem.requiredHours || classItem.credits) {
    //   contentLines.push('')
    // }
    //
    // // 班次描述 - 优化长文本显示
    // if (classItem.description) {
    //   contentLines.push(`📄 班次描述`)
    //   // 对描述文本进行适当的换行处理
    //   const description = classItem.description.toString()
    //   const maxLineLength = 25 // 每行最大字符数
    //   const words = description.split('')
    //   let currentLine = '   '
    //
    //   for (let i = 0; i < words.length; i++) {
    //     currentLine += words[i]
    //     if (currentLine.length >= maxLineLength && words[i] !== ' ') {
    //       contentLines.push(currentLine)
    //       currentLine = '   '
    //     }
    //   }
    //   if (currentLine.trim().length > 0) {
    //     contentLines.push(currentLine)
    //   }
    // }
    //
    // // 移除最后的空行
    // while (contentLines.length > 0 && contentLines[contentLines.length - 1] === '') {
    //   contentLines.pop()
    // }
    //
    // const content = contentLines.join('\n')
    
    wx.showModal({
      title: `📚 ${classItem.className}`,
      content: content,
      showCancel: classItem.canRegister,
      cancelText: '取消',
      confirmText: classItem.canRegister ? '立即报名' : '我知道了',
      success: (res) => {
        if (res.confirm && classItem.canRegister) {
          // 如果点击确认且可以报名，直接执行报名操作
          this.handleRegister({
            currentTarget: {
              dataset: { classItem }
            }
          })
        }
      }
    })
  },

  // 查看报名详情
  viewRegistrationDetail(e) {
    const { registration } = e.currentTarget.dataset
    console.log('registration:', registration)
    if (!registration || !registration.registrationId || isNaN(Number(registration.registrationId))) {
      wx.showToast({
        title: '报名ID异常',
        icon: 'none'
      })
      return
    }
    wx.navigateTo({
      url: `/pages/registration/detail?registrationId=${registration.registrationId}`
    })
  }
}) 