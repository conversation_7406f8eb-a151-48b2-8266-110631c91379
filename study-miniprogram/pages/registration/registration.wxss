.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 标签页 */
.tab-bar {
  display: flex;
  background-color: #fff;
  border-radius: 10rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 24rpx 0;
  font-size: 28rpx;
  color: #666;
  background-color: #fff;
  transition: all 0.3s;
}

.tab-item.active {
  color: #409EFF;
  background-color: #ECF5FF;
  font-weight: bold;
}

/* 内容区域 */
.tab-content {
  min-height: 600rpx;
}

.loading, .empty {
  text-align: center;
  padding: 100rpx 0;
  color: #999;
  font-size: 28rpx;
}

/* 班次列表 */
.class-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.class-item {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.class-header, .registration-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.class-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  line-height: 1.4;
  flex: 1;
}

.status-badge {
  font-size: 24rpx;
  font-weight: 500;
  padding: 6rpx 12rpx;
  border-radius: 16rpx;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  height: 32rpx;
  line-height: 1;
  white-space: nowrap;
  flex-shrink: 0;
}

.class-info {
  margin-bottom: 20rpx;
}

.info-row {
  display: flex;
  margin-bottom: 12rpx;
  font-size: 26rpx;
}

.info-row .label {
  color: #666;
  width: 160rpx;
  flex-shrink: 0;
}

.info-row .value {
  color: #333;
  flex: 1;
  word-wrap: break-word;
  word-break: break-all;
  white-space: pre-wrap;
}

.class-actions {
  text-align: right;
}



.status-success { 
  background-color: #f6ffed; 
  color: #52c41a; 
  border: 1rpx solid #b7eb8f;
}
.status-warning { 
  background-color: #fffbe6; 
  color: #faad14; 
  border: 1rpx solid #ffe58f;
}
.status-danger { 
  background-color: #fff2f0; 
  color: #ff4d4f; 
  border: 1rpx solid #ffccc7;
}
.status-info { 
  background-color: #f0f5ff; 
  color: #1890ff; 
  border: 1rpx solid #adc6ff;
}
.status-pending { 
  background-color: #fffbe6; 
  color: #faad14; 
  border: 1rpx solid #ffe58f;
}
.status-approved { 
  background-color: #f6ffed; 
  color: #52c41a; 
  border: 1rpx solid #b7eb8f;
}
.status-rejected { 
  background-color: #fff2f0; 
  color: #ff4d4f; 
  border: 1rpx solid #ffccc7;
}

/* 特殊数值样式 */
.completion-rate {
  font-weight: bold;
  color: #52c41a;
}

.score {
  font-weight: bold;
  color: #1890ff;
}

.remark {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
  word-break: break-all;
}

.register-btn {
  height: 44rpx;
  line-height: 44rpx;
  font-size: 22rpx;
  border-radius: 22rpx;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  margin: 0;
  padding: 0 24rpx;
  box-sizing: border-box;
}

.register-btn.disabled {
  background-color: #ccc !important;
  color: #999 !important;
}

/* 报名列表 */
.registration-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.registration-item {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.registration-info {
  color: #666;
}