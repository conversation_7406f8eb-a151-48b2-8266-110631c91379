const app = getApp()

Page({
  data: {
    detail: {}
  },

  onLoad(options) {
    const registrationId = options.registrationId
    if (!registrationId) {
      wx.showToast({ title: '参数错误', icon: 'none' })
      wx.navigateBack()
      return
    }
    this.loadDetail(registrationId)
  },

  async loadDetail(registrationId) {
    try {
      wx.showLoading({ title: '加载中' })
      // 获取报名详情
      const res = await app.request({
        url: '/study/registration/detail',
        data: { registrationId }
      })
      let detail = res.data || {}
      // 获取班级信息
      if (detail.classId) {
        const classRes = await app.request({
          url: '/study/class/list',
          data: { classId: detail.classId }
        })
        if (classRes.rows && classRes.rows.length > 0) {
          const classInfo = classRes.rows[0]
          detail = {
            ...detail,
            className: classInfo.className,
            displayStartDate: classInfo.classStartDate,
            displayEndDate: classInfo.classEndDate,
            requiredHours: classInfo.requiredHours,
            credits: classInfo.credits,
            description: classInfo.description
          }
        }
      }
      // 状态样式
      detail.statusText = this.getRegistrationStatusText(detail.status)
      detail.statusClass = this.getRegistrationStatusClass(detail.status)
      this.setData({ detail })
    } catch (e) {
      wx.showToast({ title: '加载失败', icon: 'none' })
    } finally {
      wx.hideLoading()
    }
  },

  getRegistrationStatusText(status) {
    const statusMap = {
      '0': '待审核',
      '1': '已通过', 
      '2': '已拒绝',
      '3': '学习中',
      '4': '已结业',
      '5': '已退班'
    }
    return statusMap[status] || '未知状态'
  },
  getRegistrationStatusClass(status) {
    const classMap = {
      '0': 'status-warning', // 待审核 - 橙色
      '1': 'status-success', // 已通过 - 绿色
      '2': 'status-danger',  // 已拒绝 - 红色
      '3': 'status-info',    // 学习中 - 蓝色
      '4': 'status-success', // 已结业 - 绿色
      '5': 'status-danger'   // 已退班 - 红色
    }
    return classMap[status] || 'status-info'
  },

  goBack() {
    wx.navigateBack()
  }
}) 