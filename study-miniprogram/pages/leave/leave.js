const app = getApp()

Page({
  data: {
    courseOptions: [],
    selectedCourseIndex: 0,
    reason: '',
    submitting: false,
    leaveRecords: [],
    userInfo: {}
  },

  onLoad() {
    this.loadUserInfo()
    this.loadCourseOptions()
    this.loadLeaveRecords()
  },

  onShow() {
    this.loadLeaveRecords()
  },

  // 加载用户信息
  loadUserInfo() {
    const userInfo = wx.getStorageSync('userInfo')
    if (userInfo) {
      this.setData({ userInfo })
    }
  },

  // 加载课堂选项
  async loadCourseOptions() {
    try {
      const res = await app.request({
        url: '/study/course/list',
        data: {
          pageNum: 1,
          pageSize: 100
        }
      })

      this.setData({
        courseOptions: res.rows || []
      })
    } catch (error) {
      console.error('加载课堂列表失败:', error)
    }
  },

  // 课堂选择
  onCourseChange(e) {
    this.setData({
      selectedCourseIndex: parseInt(e.detail.value)
    })
  },

  // 请假原因输入
  onReasonInput(e) {
    this.setData({
      reason: e.detail.value
    })
  },

  // 提交请假申请
  async submitLeave() {
    const { courseOptions, selectedCourseIndex, reason } = this.data

    // 验证输入
    if (!courseOptions || courseOptions.length === 0) {
      wx.showToast({
        title: '暂无可请假的课堂',
        icon: 'none'
      })
      return
    }

    if (!reason.trim()) {
      wx.showToast({
        title: '请输入请假事由',
        icon: 'none'
      })
      return
    }

    const selectedCourse = courseOptions[selectedCourseIndex]
    if (!selectedCourse) {
      wx.showToast({
        title: '请选择课堂',
        icon: 'none'
      })
      return
    }

    this.setData({ submitting: true })

    try {
      await app.request({
        url: '/study/leaveRequest',
        method: 'POST',
        data: {
          courseId: selectedCourse.courseId,
          reason: reason.trim(),
          hours: selectedCourse.hours || 0,
          auditStatus: '0' // 待审批
        }
      })

      wx.showToast({
        title: '申请提交成功',
        icon: 'success'
      })

      // 重置表单
      this.setData({
        selectedCourseIndex: 0,
        reason: ''
      })

      // 刷新记录
      this.loadLeaveRecords()

    } catch (error) {
      wx.showToast({
        title: error || '申请提交失败',
        icon: 'none'
      })
    } finally {
      this.setData({ submitting: false })
    }
  },

  // 加载请假记录
  async loadLeaveRecords() {
    try {
      const userInfo = wx.getStorageSync('userInfo')
      const res = await app.request({
        url: '/study/leaveRequest/list',
        data: {
          pageNum: 1,
          pageSize: 20,
          userId: userInfo.userId
        }
      })

      const records = (res.rows || []).map(item => ({
        ...item,
        statusText: this.getStatusText(item.auditStatus),
        statusClass: this.getStatusClass(item.auditStatus)
      }))

      this.setData({
        leaveRecords: records
      })
    } catch (error) {
      console.error('加载请假记录失败:', error)
    }
  },

  // 获取状态样式类
  getStatusClass(status) {
    switch (status) {
      case '0': return 'status-pending'
      case '1': return 'status-approved'
      case '2': return 'status-rejected'
      default: return ''
    }
  },

  // 获取状态文字
  getStatusText(status) {
    switch (status) {
      case '0': return '待审批'
      case '1': return '已批准'
      case '2': return '已驳回'
      default: return '未知'
    }
  },

  // 查看记录详情
  viewRecord(e) {
    const record = e.currentTarget.dataset.record
    wx.showModal({
      title: '请假详情',
      content: `课堂：${record.courseName || '未知'}\n事由：${record.reason}\n学时：${record.hours}小时\n状态：${this.getStatusText(record.auditStatus)}`,
      showCancel: false
    })
  }
}) 