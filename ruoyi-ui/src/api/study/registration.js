import request from '@/utils/request'

// 查询学员报名列表
export function listRegistration(query) {
  return request({
    url: '/study/registration/list',
    method: 'get',
    params: query
  })
}

// 查询学员报名详细
export function getRegistration(registrationId) {
  return request({
    url: '/study/registration/' + registrationId,
    method: 'get'
  })
}

// 新增学员报名
export function addRegistration(data) {
  return request({
    url: '/study/registration',
    method: 'post',
    data: data
  })
}

// 修改学员报名
export function updateRegistration(data) {
  return request({
    url: '/study/registration',
    method: 'put',
    data: data
  })
}

// 删除学员报名
export function delRegistration(registrationId) {
  return request({
    url: '/study/registration/' + registrationId,
    method: 'delete'
  })
}

// 需要根据用户ID和班次ID，找到对应的报名ID
export function getRegistrationByUserAndClass(userId,classId) {
  return request({
    url: '/study/registration/getRegistrationByUserAndClass?userId='+userId+'&classId='+classId,
    method: 'get'
  })
}

// 批准报名申请
export function approveRegistration(registrationId) {
  return request({
    url: '/study/registration/approve/' + registrationId,
    method: 'put'
  })
}

// 拒绝报名申请
export function rejectRegistration(registrationId, reason) {
  return request({
    url: '/study/registration/reject/' + registrationId,
    method: 'put',
    data: reason
  })
}

// 批量审批报名申请
export function batchApproveRegistration(registrationIds) {
  return request({
    url: '/study/registration/batchApprove',
    method: 'put',
    data: registrationIds
  })
}

// 开始学习
export function startStudy(registrationId) {
  return request({
    url: '/study/registration/startStudy/' + registrationId,
    method: 'put'
  })
}

// 学员结业
export function graduate(registrationId) {
  return request({
    url: '/study/registration/graduate/' + registrationId,
    method: 'put'
  })
}
